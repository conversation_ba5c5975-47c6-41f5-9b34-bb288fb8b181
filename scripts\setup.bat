@echo off
echo ========================================
echo FireTool Setup Script for Windows
echo ========================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please download and install Node.js from https://nodejs.org/
    echo Recommended version: Node.js 18 LTS or higher
    pause
    exit /b 1
)

:: Display Node.js version
echo Node.js version:
node --version
echo.

:: Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available
    echo Please reinstall Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Display npm version
echo npm version:
npm --version
echo.

:: Navigate to project directory
cd /d "%~dp0.."

:: Install dependencies
echo Installing dependencies...
echo This may take a few minutes...
echo.
npm install

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to install dependencies
    echo.
    echo Common solutions:
    echo 1. Run this script as Administrator
    echo 2. Install Visual Studio Build Tools for native modules
    echo 3. Check your internet connection
    echo 4. Clear npm cache: npm cache clean --force
    echo.
    pause
    exit /b 1
)

:: Create assets directory if it doesn't exist
if not exist "assets" (
    echo Creating assets directory...
    mkdir assets
)

:: Check if basic assets exist
if not exist "assets\icon.png" (
    echo.
    echo WARNING: Missing application icon (assets\icon.png)
    echo You'll need to add icon files for the application to work properly.
    echo See assets\README.md for requirements.
    echo.
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo To start the application:
echo   npm run dev    (development mode)
echo   npm start      (production mode)
echo.
echo To build for distribution:
echo   npm run build
echo.
echo For more information, see SETUP.md
echo.
pause
