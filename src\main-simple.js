const { app, BrowserWindow, <PERSON>u, ipcMain, dialog } = require('electron');
const path = require('path');
const DataManager = require('./data/dataManager');

let mainWindow;
let dataManager;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    show: false
  });

  mainWindow.loadFile(path.join(__dirname, 'renderer/index-app.html'));

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    console.log('🔥 FireTool is running!');
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

app.whenReady().then(() => {
  // Initialize data manager
  dataManager = new DataManager();

  // Set up IPC handlers
  setupIpcHandlers();

  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC handlers for data operations
function setupIpcHandlers() {
  // Project handlers
  ipcMain.handle('get-projects', () => dataManager.getProjects());
  ipcMain.handle('get-project', (event, id) => dataManager.getProject(id));
  ipcMain.handle('add-project', (event, project) => dataManager.addProject(project));
  ipcMain.handle('update-project', (event, id, updates) => dataManager.updateProject(id, updates));
  ipcMain.handle('delete-project', (event, id) => dataManager.deleteProject(id));

  // Item handlers
  ipcMain.handle('get-items', () => dataManager.getItems());
  ipcMain.handle('get-item', (event, id) => dataManager.getItem(id));
  ipcMain.handle('add-item', (event, item) => dataManager.addItem(item));
  ipcMain.handle('update-item', (event, id, updates) => dataManager.updateItem(id, updates));
  ipcMain.handle('delete-item', (event, id) => dataManager.deleteItem(id));

  // Category handlers
  ipcMain.handle('get-categories', () => dataManager.getCategories());

  // Settings handlers
  ipcMain.handle('get-settings', () => dataManager.getSettings());
  ipcMain.handle('update-settings', (event, updates) => dataManager.updateSettings(updates));

  // Data export/import handlers
  ipcMain.handle('export-data', () => dataManager.exportData());
  ipcMain.handle('import-data', (event, jsonData) => dataManager.importData(jsonData));
  ipcMain.handle('get-data-path', () => dataManager.getDataPath());
}

console.log('🔥 FireTool starting...');
