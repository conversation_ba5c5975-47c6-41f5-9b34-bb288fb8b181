// Database Interface for Renderer Process
const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class DatabaseInterface {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  // Generic database operations
  async query(sql, params = []) {
    try {
      return await ipc<PERSON>enderer.invoke('db-query', sql, params);
    } catch (error) {
      console.error('Database query failed:', error);
      throw error;
    }
  }

  async insert(table, data) {
    try {
      return await ipcRenderer.invoke('db-insert', table, data);
    } catch (error) {
      console.error('Database insert failed:', error);
      throw error;
    }
  }

  async update(table, id, data) {
    try {
      const result = await ipcRenderer.invoke('db-update', table, id, data);
      this.invalidateCache(table);
      return result;
    } catch (error) {
      console.error('Database update failed:', error);
      throw error;
    }
  }

  async delete(table, id) {
    try {
      const result = await ipc<PERSON>enderer.invoke('db-delete', table, id);
      this.invalidateCache(table);
      return result;
    } catch (error) {
      console.error('Database delete failed:', error);
      throw error;
    }
  }

  async findById(table, id) {
    try {
      return await ipcRenderer.invoke('db-find-by-id', table, id);
    } catch (error) {
      console.error('Database findById failed:', error);
      throw error;
    }
  }

  async findAll(table, conditions = {}) {
    const cacheKey = `${table}_${JSON.stringify(conditions)}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    try {
      const result = await ipcRenderer.invoke('db-find-all', table, conditions);
      
      // Cache the result
      this.cache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      });
      
      return result;
    } catch (error) {
      console.error('Database findAll failed:', error);
      throw error;
    }
  }

  // Dashboard specific methods
  async getDashboardStats() {
    try {
      const [products, suppliers, projects, estimates] = await Promise.all([
        this.query('SELECT COUNT(*) as count FROM products WHERE is_active = 1'),
        this.query('SELECT COUNT(*) as count FROM suppliers WHERE is_active = 1'),
        this.query('SELECT COUNT(*) as count FROM projects WHERE is_active = 1'),
        this.query('SELECT COUNT(*) as count FROM estimates')
      ]);

      return {
        totalProducts: products[0]?.count || 0,
        totalSuppliers: suppliers[0]?.count || 0,
        totalProjects: projects[0]?.count || 0,
        totalEstimates: estimates[0]?.count || 0
      };
    } catch (error) {
      console.error('Failed to get dashboard stats:', error);
      return {
        totalProducts: 0,
        totalSuppliers: 0,
        totalProjects: 0,
        totalEstimates: 0
      };
    }
  }

  async getRecentActivity(limit = 10) {
    try {
      const activities = await this.query(`
        SELECT 
          'Product added: ' || name as description,
          created_at
        FROM products 
        WHERE created_at >= datetime('now', '-7 days')
        
        UNION ALL
        
        SELECT 
          'Project created: ' || name as description,
          created_at
        FROM projects 
        WHERE created_at >= datetime('now', '-7 days')
        
        UNION ALL
        
        SELECT 
          'Estimate generated: ' || title as description,
          created_at
        FROM estimates 
        WHERE created_at >= datetime('now', '-7 days')
        
        ORDER BY created_at DESC 
        LIMIT ?
      `, [limit]);

      return activities;
    } catch (error) {
      console.error('Failed to get recent activity:', error);
      return [];
    }
  }

  async getCategoryDistribution() {
    try {
      const distribution = await this.query(`
        SELECT 
          c.name,
          COUNT(p.id) as count
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
        GROUP BY c.id, c.name
        HAVING count > 0
        ORDER BY count DESC
      `);

      return distribution;
    } catch (error) {
      console.error('Failed to get category distribution:', error);
      return [];
    }
  }

  // Product specific methods
  async getAllProducts() {
    return this.findAll('products', { is_active: 1 });
  }

  async getProductsByCategory(categoryId) {
    return this.findAll('products', { category_id: categoryId, is_active: 1 });
  }

  async searchProducts(query) {
    try {
      const products = await this.query(`
        SELECT p.*, c.name as category_name, s.name as supplier_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        WHERE p.is_active = 1 
        AND (
          p.name LIKE ? OR 
          p.description LIKE ? OR 
          p.code LIKE ? OR
          c.name LIKE ? OR
          s.name LIKE ?
        )
        ORDER BY p.name
      `, Array(5).fill(`%${query}%`));

      return products;
    } catch (error) {
      console.error('Failed to search products:', error);
      return [];
    }
  }

  async addProduct(productData) {
    try {
      const result = await this.insert('products', {
        ...productData,
        id: this.generateId(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: 1
      });

      this.invalidateCache('products');
      return result;
    } catch (error) {
      console.error('Failed to add product:', error);
      throw error;
    }
  }

  async updateProduct(id, productData) {
    try {
      const result = await this.update('products', id, {
        ...productData,
        updated_at: new Date().toISOString()
      });

      return result;
    } catch (error) {
      console.error('Failed to update product:', error);
      throw error;
    }
  }

  async deleteProduct(id) {
    try {
      // Soft delete
      return await this.update('products', id, {
        is_active: 0,
        updated_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to delete product:', error);
      throw error;
    }
  }

  // Category methods
  async getAllCategories() {
    return this.findAll('categories');
  }

  async addCategory(categoryData) {
    try {
      return await this.insert('categories', {
        ...categoryData,
        id: this.generateId(),
        created_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to add category:', error);
      throw error;
    }
  }

  // Supplier methods
  async getAllSuppliers() {
    return this.findAll('suppliers', { is_active: 1 });
  }

  async addSupplier(supplierData) {
    try {
      return await this.insert('suppliers', {
        ...supplierData,
        id: this.generateId(),
        created_at: new Date().toISOString(),
        is_active: 1
      });
    } catch (error) {
      console.error('Failed to add supplier:', error);
      throw error;
    }
  }

  // BOQ methods
  async getAllBOQs() {
    return this.findAll('boq');
  }

  async getBOQById(id) {
    return this.findById('boq', id);
  }

  async getBOQItems(boqId) {
    try {
      const items = await this.query(`
        SELECT 
          bi.*,
          p.name as product_name,
          p.code as product_code,
          p.unit as product_unit
        FROM boq_items bi
        LEFT JOIN products p ON bi.product_id = p.id
        WHERE bi.boq_id = ?
        ORDER BY bi.sort_order, bi.item_description
      `, [boqId]);

      return items;
    } catch (error) {
      console.error('Failed to get BOQ items:', error);
      return [];
    }
  }

  async addBOQ(boqData) {
    try {
      return await this.insert('boq', {
        ...boqData,
        id: this.generateId(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 'draft',
        total_amount: 0
      });
    } catch (error) {
      console.error('Failed to add BOQ:', error);
      throw error;
    }
  }

  async addBOQItem(itemData) {
    try {
      const result = await this.insert('boq_items', {
        ...itemData,
        id: this.generateId(),
        total_price: itemData.quantity * itemData.unit_price
      });

      // Update BOQ total
      await this.updateBOQTotal(itemData.boq_id);
      
      return result;
    } catch (error) {
      console.error('Failed to add BOQ item:', error);
      throw error;
    }
  }

  async updateBOQTotal(boqId) {
    try {
      const totalResult = await this.query(`
        SELECT SUM(total_price) as total
        FROM boq_items
        WHERE boq_id = ?
      `, [boqId]);

      const total = totalResult[0]?.total || 0;
      
      await this.update('boq', boqId, {
        total_amount: total,
        updated_at: new Date().toISOString()
      });

      return total;
    } catch (error) {
      console.error('Failed to update BOQ total:', error);
      throw error;
    }
  }

  // Project methods
  async getAllProjects() {
    return this.findAll('projects', { is_active: 1 });
  }

  async addProject(projectData) {
    try {
      return await this.insert('projects', {
        ...projectData,
        id: this.generateId(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: 1
      });
    } catch (error) {
      console.error('Failed to add project:', error);
      throw error;
    }
  }

  // Estimate methods
  async getAllEstimates() {
    return this.findAll('estimates');
  }

  async addEstimate(estimateData) {
    try {
      return await this.insert('estimates', {
        ...estimateData,
        id: this.generateId(),
        estimate_number: this.generateEstimateNumber(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 'draft'
      });
    } catch (error) {
      console.error('Failed to add estimate:', error);
      throw error;
    }
  }

  // Import/Export methods
  async importData(data, tableName) {
    try {
      const results = [];
      
      for (const item of data) {
        const result = await this.insert(tableName, {
          ...item,
          id: this.generateId(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
        results.push(result);
      }

      this.invalidateCache(tableName);
      return results;
    } catch (error) {
      console.error('Failed to import data:', error);
      throw error;
    }
  }

  // Utility methods
  generateId() {
    return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateEstimateNumber() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const time = String(date.getTime()).slice(-4);
    
    return `EST-${year}${month}${day}-${time}`;
  }

  invalidateCache(table) {
    // Remove all cache entries for the specified table
    for (const key of this.cache.keys()) {
      if (key.startsWith(table + '_')) {
        this.cache.delete(key);
      }
    }
  }

  clearCache() {
    this.cache.clear();
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DatabaseInterface;
}
