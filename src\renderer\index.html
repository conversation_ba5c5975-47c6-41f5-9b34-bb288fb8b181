<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireTool - Cost Estimation Platform</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/grid.css">
    <link rel="stylesheet" href="styles/components.css">
    
    <!-- AG Grid CSS -->
    <link rel="stylesheet" href="../node_modules/ag-grid-community/styles/ag-grid.css">
    <link rel="stylesheet" href="../node_modules/ag-grid-community/styles/ag-theme-alpine.css">
    
    <!-- Chart.js -->
    <script src="../node_modules/chart.js/dist/chart.min.js"></script>
    <script src="../node_modules/chartjs-adapter-moment/dist/chartjs-adapter-moment.min.js"></script>
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <img src="../assets/logo.png" alt="FireTool" class="logo">
                <h1>FireTool</h1>
            </div>
            <div class="header-center">
                <div class="project-info">
                    <span id="current-project">No Project Loaded</span>
                </div>
            </div>
            <div class="header-right">
                <button id="sync-btn" class="btn btn-primary" title="Sync with Supabase">
                    <i class="icon-sync"></i> Sync
                </button>
                <div class="sync-status" id="sync-status">
                    <span class="status-indicator offline"></span>
                    <span class="status-text">Offline</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-container">
            <!-- Sidebar -->
            <aside class="sidebar">
                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <h3>Project</h3>
                        <ul>
                            <li><a href="#" data-view="dashboard" class="nav-link active">Dashboard</a></li>
                            <li><a href="#" data-view="projects" class="nav-link">Projects</a></li>
                        </ul>
                    </div>
                    
                    <div class="nav-section">
                        <h3>Database</h3>
                        <ul>
                            <li><a href="#" data-view="products" class="nav-link">Products</a></li>
                            <li><a href="#" data-view="suppliers" class="nav-link">Suppliers</a></li>
                            <li><a href="#" data-view="labor-rates" class="nav-link">Labor Rates</a></li>
                            <li><a href="#" data-view="categories" class="nav-link">Categories</a></li>
                        </ul>
                    </div>
                    
                    <div class="nav-section">
                        <h3>Fire Safety Systems</h3>
                        <ul>
                            <li><a href="#" data-category="fire-alarm" class="nav-link">Fire Alarm</a></li>
                            <li><a href="#" data-category="firefighting" class="nav-link">Firefighting</a></li>
                            <li><a href="#" data-category="clean-agent" class="nav-link">Clean Agent</a></li>
                            <li><a href="#" data-category="foam" class="nav-link">Foam Systems</a></li>
                            <li><a href="#" data-category="co2" class="nav-link">CO2 Systems</a></li>
                            <li><a href="#" data-category="fire-pumps" class="nav-link">Fire Pumps</a></li>
                            <li><a href="#" data-category="foam-pumps" class="nav-link">Foam Pumps</a></li>
                            <li><a href="#" data-category="civil-works" class="nav-link">Civil Works</a></li>
                        </ul>
                    </div>
                    
                    <div class="nav-section">
                        <h3>Estimation</h3>
                        <ul>
                            <li><a href="#" data-view="boq" class="nav-link">BOQ Builder</a></li>
                            <li><a href="#" data-view="estimates" class="nav-link">Estimates</a></li>
                            <li><a href="#" data-view="reports" class="nav-link">Reports</a></li>
                        </ul>
                    </div>
                </nav>
            </aside>

            <!-- Content Area -->
            <main class="content-area">
                <!-- Dashboard View -->
                <div id="dashboard-view" class="view active">
                    <div class="view-header">
                        <h2>Dashboard</h2>
                        <div class="view-actions">
                            <button class="btn btn-primary" id="import-excel-btn">
                                <i class="icon-upload"></i> Import Excel
                            </button>
                            <button class="btn btn-secondary" id="export-excel-btn">
                                <i class="icon-download"></i> Export Excel
                            </button>
                        </div>
                    </div>
                    
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <h3>Quick Stats</h3>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <span class="stat-value" id="total-products">0</span>
                                    <span class="stat-label">Products</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value" id="total-suppliers">0</span>
                                    <span class="stat-label">Suppliers</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value" id="total-projects">0</span>
                                    <span class="stat-label">Projects</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value" id="total-estimates">0</span>
                                    <span class="stat-label">Estimates</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <h3>Recent Activity</h3>
                            <div id="recent-activity" class="activity-list">
                                <!-- Activity items will be populated here -->
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <h3>Category Distribution</h3>
                            <canvas id="category-chart" width="400" height="200"></canvas>
                        </div>
                        
                        <div class="dashboard-card">
                            <h3>Quick Actions</h3>
                            <div class="quick-actions">
                                <button class="btn btn-outline" data-action="new-project">New Project</button>
                                <button class="btn btn-outline" data-action="new-boq">Create BOQ</button>
                                <button class="btn btn-outline" data-action="new-estimate">New Estimate</button>
                                <button class="btn btn-outline" data-action="import-data">Import Data</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products View -->
                <div id="products-view" class="view">
                    <div class="view-header">
                        <h2>Products Catalog</h2>
                        <div class="view-actions">
                            <button class="btn btn-primary" id="add-product-btn">
                                <i class="icon-plus"></i> Add Product
                            </button>
                            <button class="btn btn-secondary" id="bulk-edit-btn">
                                <i class="icon-edit"></i> Bulk Edit
                            </button>
                        </div>
                    </div>
                    
                    <div class="toolbar">
                        <div class="search-box">
                            <input type="text" id="product-search" placeholder="Search products...">
                            <i class="icon-search"></i>
                        </div>
                        <div class="filters">
                            <select id="category-filter">
                                <option value="">All Categories</option>
                            </select>
                            <select id="supplier-filter">
                                <option value="">All Suppliers</option>
                            </select>
                        </div>
                    </div>
                    
                    <div id="products-grid" class="ag-theme-alpine data-grid"></div>
                </div>

                <!-- BOQ Builder View -->
                <div id="boq-view" class="view">
                    <div class="view-header">
                        <h2>BOQ Builder</h2>
                        <div class="view-actions">
                            <button class="btn btn-primary" id="save-boq-btn">
                                <i class="icon-save"></i> Save BOQ
                            </button>
                            <button class="btn btn-secondary" id="export-boq-btn">
                                <i class="icon-download"></i> Export
                            </button>
                        </div>
                    </div>
                    
                    <div class="boq-container">
                        <div class="boq-sidebar">
                            <h3>Product Catalog</h3>
                            <div class="catalog-search">
                                <input type="text" id="catalog-search" placeholder="Search catalog...">
                            </div>
                            <div id="catalog-tree" class="catalog-tree">
                                <!-- Product catalog tree will be populated here -->
                            </div>
                        </div>
                        
                        <div class="boq-main">
                            <div class="boq-header">
                                <input type="text" id="boq-title" placeholder="BOQ Title" class="boq-title-input">
                                <div class="boq-info">
                                    <span>Total Items: <span id="boq-item-count">0</span></span>
                                    <span>Total Amount: $<span id="boq-total-amount">0.00</span></span>
                                </div>
                            </div>
                            
                            <div id="boq-grid" class="ag-theme-alpine data-grid"></div>
                        </div>
                    </div>
                </div>

                <!-- Other views will be added here -->
            </main>
        </div>

        <!-- Modals -->
        <div id="modal-overlay" class="modal-overlay">
            <div id="modal-container" class="modal-container">
                <!-- Modal content will be dynamically loaded here -->
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loading-indicator" class="loading-indicator">
            <div class="spinner"></div>
            <span>Loading...</span>
        </div>

        <!-- Notifications -->
        <div id="notifications" class="notifications-container">
            <!-- Notifications will be dynamically added here -->
        </div>
    </div>

    <!-- Scripts -->
    <script src="../node_modules/ag-grid-community/dist/ag-grid-community.min.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/database.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/grid.js"></script>
    <script src="js/boq.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
