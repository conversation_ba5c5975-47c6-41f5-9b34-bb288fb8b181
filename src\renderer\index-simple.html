<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 FireTool - Cost Estimation Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 60px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 800px;
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 300;
        }
        
        .subtitle {
            font-size: 1.5rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            transition: transform 0.3s ease;
        }
        
        .feature:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .feature h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }
        
        .feature p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .status {
            margin-top: 40px;
            padding: 20px;
            background: rgba(46, 204, 113, 0.2);
            border-radius: 10px;
            border: 1px solid rgba(46, 204, 113, 0.3);
        }
        
        .version {
            margin-top: 20px;
            opacity: 0.7;
            font-size: 0.9rem;
        }
        
        .next-steps {
            margin-top: 30px;
            text-align: left;
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
        }
        
        .next-steps h3 {
            margin-bottom: 15px;
            color: #ffd700;
        }
        
        .next-steps ol {
            margin-left: 20px;
        }
        
        .next-steps li {
            margin-bottom: 8px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔥</div>
        <h1>FireTool</h1>
        <p class="subtitle">Cost Estimation & BOQ Generation Platform</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h3>Product Catalog</h3>
                <p>Manage fire safety equipment across all categories</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📋</div>
                <h3>BOQ Builder</h3>
                <p>Create professional Bills of Quantities with drag & drop</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">💰</div>
                <h3>Cost Estimation</h3>
                <p>Generate accurate estimates with markup calculations</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📤</div>
                <h3>Excel Integration</h3>
                <p>Import/export data seamlessly with Excel files</p>
            </div>
        </div>
        
        <div class="status">
            <h3>✅ Application Successfully Running!</h3>
            <p>FireTool desktop application is now active and ready for use.</p>
        </div>
        
        <div class="next-steps">
            <h3>🚀 Next Steps:</h3>
            <ol>
                <li><strong>Add Dependencies:</strong> Install Excel processing and database libraries</li>
                <li><strong>Import Data:</strong> Load your fire safety product catalogs</li>
                <li><strong>Create BOQs:</strong> Start building your first Bill of Quantities</li>
                <li><strong>Generate Estimates:</strong> Create professional cost estimates</li>
                <li><strong>Setup Cloud Sync:</strong> Configure Supabase for team collaboration</li>
            </ol>
        </div>
        
        <div class="version">
            FireTool v1.0.0 - Desktop Edition<br>
            Built with Electron.js for Windows, macOS & Linux
        </div>
    </div>
    
    <script>
        console.log('🔥 FireTool renderer loaded successfully!');
        
        // Simple demo functionality
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔥 FireTool is ready!');
            
            // Add some interactivity
            const features = document.querySelectorAll('.feature');
            features.forEach(feature => {
                feature.addEventListener('click', () => {
                    feature.style.background = 'rgba(255, 255, 255, 0.2)';
                    setTimeout(() => {
                        feature.style.background = 'rgba(255, 255, 255, 0.1)';
                    }, 200);
                });
            });
        });
    </script>
</body>
</html>
