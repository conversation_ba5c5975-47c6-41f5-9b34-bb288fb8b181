// FireTool Main Application
const { ipc<PERSON>enderer } = require('electron');

class FireToolApp {
  constructor() {
    this.currentView = 'dashboard';
    this.currentProject = null;
    this.database = null;
    this.grids = {};
    this.isOnline = false;
    
    this.init();
  }

  async init() {
    try {
      // Show loading indicator
      this.showLoading('Initializing FireTool...');
      
      // Initialize components
      await this.initializeDatabase();
      this.initializeUI();
      this.initializeEventListeners();
      this.initializeGrids();
      
      // Load initial data
      await this.loadDashboardData();
      
      // Hide loading indicator
      this.hideLoading();
      
      console.log('FireTool initialized successfully');
    } catch (error) {
      console.error('Failed to initialize FireTool:', error);
      this.showNotification('Error', 'Failed to initialize application', 'error');
      this.hideLoading();
    }
  }

  async initializeDatabase() {
    return new Promise((resolve) => {
      ipcRenderer.once('database-ready', () => {
        this.database = new DatabaseInterface();
        resolve();
      });
      
      // Database should be initialized by main process
      setTimeout(() => {
        if (!this.database) {
          this.database = new DatabaseInterface();
          resolve();
        }
      }, 2000);
    });
  }

  initializeUI() {
    // Initialize navigation
    this.initializeNavigation();
    
    // Initialize modals
    this.initializeModals();
    
    // Initialize drag and drop
    this.initializeDragDrop();
    
    // Set initial view
    this.showView('dashboard');
  }

  initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        
        const view = link.dataset.view;
        const category = link.dataset.category;
        
        if (view) {
          this.showView(view);
        } else if (category) {
          this.showCategoryProducts(category);
        }
        
        // Update active state
        navLinks.forEach(l => l.classList.remove('active'));
        link.classList.add('active');
      });
    });
  }

  initializeEventListeners() {
    // Menu events from main process
    ipcRenderer.on('menu-new-project', () => this.newProject());
    ipcRenderer.on('menu-open-project', (event, filePath) => this.openProject(filePath));
    ipcRenderer.on('menu-save-project', () => this.saveProject());
    ipcRenderer.on('menu-save-project-as', (event, filePath) => this.saveProjectAs(filePath));
    ipcRenderer.on('menu-import-excel', (event, filePath) => this.importExcel(filePath));
    ipcRenderer.on('menu-export-excel', () => this.exportExcel());
    ipcRenderer.on('menu-sync-database', () => this.syncDatabase());
    ipcRenderer.on('menu-backup-database', () => this.backupDatabase());
    ipcRenderer.on('menu-restore-database', () => this.restoreDatabase());

    // Button events
    document.getElementById('import-excel-btn')?.addEventListener('click', () => this.importExcel());
    document.getElementById('export-excel-btn')?.addEventListener('click', () => this.exportExcel());
    document.getElementById('add-product-btn')?.addEventListener('click', () => this.addProduct());
    document.getElementById('sync-btn')?.addEventListener('click', () => this.syncDatabase());

    // Search events
    document.getElementById('product-search')?.addEventListener('input', (e) => {
      this.filterProducts(e.target.value);
    });

    // Quick actions
    document.querySelectorAll('[data-action]').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = e.target.dataset.action;
        this.handleQuickAction(action);
      });
    });

    // Window events
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });
  }

  initializeGrids() {
    // Initialize AG Grid instances
    this.grids.products = new ProductGrid('products-grid');
    this.grids.boq = new BOQGrid('boq-grid');
    
    // Load grid data
    this.loadGridData();
  }

  initializeModals() {
    const modalOverlay = document.getElementById('modal-overlay');
    
    modalOverlay.addEventListener('click', (e) => {
      if (e.target === modalOverlay) {
        this.hideModal();
      }
    });

    // Close button handler
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('modal-close')) {
        this.hideModal();
      }
    });

    // Escape key handler
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.hideModal();
      }
    });
  }

  initializeDragDrop() {
    // File drop handling
    document.addEventListener('dragover', (e) => {
      e.preventDefault();
    });

    document.addEventListener('drop', (e) => {
      e.preventDefault();
      
      const files = Array.from(e.dataTransfer.files);
      const excelFiles = files.filter(file => 
        file.name.endsWith('.xlsx') || 
        file.name.endsWith('.xls') || 
        file.name.endsWith('.csv')
      );

      if (excelFiles.length > 0) {
        this.importExcel(excelFiles[0].path);
      }
    });
  }

  showView(viewName) {
    // Hide all views
    document.querySelectorAll('.view').forEach(view => {
      view.classList.remove('active');
    });

    // Show target view
    const targetView = document.getElementById(`${viewName}-view`);
    if (targetView) {
      targetView.classList.add('active');
      this.currentView = viewName;
      
      // Load view-specific data
      this.loadViewData(viewName);
    }
  }

  async loadViewData(viewName) {
    switch (viewName) {
      case 'dashboard':
        await this.loadDashboardData();
        break;
      case 'products':
        await this.loadProductsData();
        break;
      case 'boq':
        await this.loadBOQData();
        break;
      default:
        break;
    }
  }

  async loadDashboardData() {
    try {
      const stats = await this.database.getDashboardStats();
      
      document.getElementById('total-products').textContent = stats.totalProducts || 0;
      document.getElementById('total-suppliers').textContent = stats.totalSuppliers || 0;
      document.getElementById('total-projects').textContent = stats.totalProjects || 0;
      document.getElementById('total-estimates').textContent = stats.totalEstimates || 0;
      
      // Load recent activity
      const recentActivity = await this.database.getRecentActivity();
      this.updateRecentActivity(recentActivity);
      
      // Load category chart
      const categoryData = await this.database.getCategoryDistribution();
      this.updateCategoryChart(categoryData);
      
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  }

  async loadProductsData() {
    try {
      const products = await this.database.getAllProducts();
      if (this.grids.products) {
        this.grids.products.setData(products);
      }
      
      // Load filter options
      const categories = await this.database.getAllCategories();
      const suppliers = await this.database.getAllSuppliers();
      
      this.updateFilterOptions('category-filter', categories);
      this.updateFilterOptions('supplier-filter', suppliers);
      
    } catch (error) {
      console.error('Failed to load products data:', error);
    }
  }

  async loadBOQData() {
    try {
      // Load product catalog for BOQ builder
      const products = await this.database.getAllProducts();
      this.updateCatalogTree(products);
      
      // Initialize empty BOQ
      if (this.grids.boq) {
        this.grids.boq.setData([]);
      }
      
    } catch (error) {
      console.error('Failed to load BOQ data:', error);
    }
  }

  async loadGridData() {
    try {
      if (this.currentView === 'products' && this.grids.products) {
        const products = await this.database.getAllProducts();
        this.grids.products.setData(products);
      }
    } catch (error) {
      console.error('Failed to load grid data:', error);
    }
  }

  updateRecentActivity(activities) {
    const container = document.getElementById('recent-activity');
    if (!container) return;

    container.innerHTML = '';
    
    if (!activities || activities.length === 0) {
      container.innerHTML = '<p class="text-muted">No recent activity</p>';
      return;
    }

    activities.forEach(activity => {
      const item = document.createElement('div');
      item.className = 'activity-item';
      item.innerHTML = `
        <span class="activity-text">${activity.description}</span>
        <span class="activity-time">${this.formatDate(activity.created_at)}</span>
      `;
      container.appendChild(item);
    });
  }

  updateCategoryChart(data) {
    const canvas = document.getElementById('category-chart');
    if (!canvas || !data) return;

    const ctx = canvas.getContext('2d');
    
    new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: data.map(item => item.name),
        datasets: [{
          data: data.map(item => item.count),
          backgroundColor: [
            '#e74c3c', '#3498db', '#2ecc71', '#f39c12',
            '#9b59b6', '#1abc9c', '#34495e', '#e67e22'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });
  }

  updateFilterOptions(selectId, options) {
    const select = document.getElementById(selectId);
    if (!select) return;

    // Clear existing options (except first)
    while (select.children.length > 1) {
      select.removeChild(select.lastChild);
    }

    options.forEach(option => {
      const optionElement = document.createElement('option');
      optionElement.value = option.id;
      optionElement.textContent = option.name;
      select.appendChild(optionElement);
    });
  }

  updateCatalogTree(products) {
    // Implementation for updating the catalog tree in BOQ builder
    // This will be expanded in the BOQ module
  }

  handleQuickAction(action) {
    switch (action) {
      case 'new-project':
        this.newProject();
        break;
      case 'new-boq':
        this.showView('boq');
        break;
      case 'new-estimate':
        this.newEstimate();
        break;
      case 'import-data':
        this.importExcel();
        break;
      default:
        console.log('Unknown action:', action);
    }
  }

  // Utility methods
  showLoading(message = 'Loading...') {
    const indicator = document.getElementById('loading-indicator');
    const text = indicator.querySelector('span');
    if (text) text.textContent = message;
    indicator.classList.add('show');
  }

  hideLoading() {
    document.getElementById('loading-indicator').classList.remove('show');
  }

  showModal(content) {
    const overlay = document.getElementById('modal-overlay');
    const container = document.getElementById('modal-container');
    
    container.innerHTML = content;
    overlay.classList.add('show');
  }

  hideModal() {
    document.getElementById('modal-overlay').classList.remove('show');
  }

  showNotification(title, message, type = 'info') {
    const container = document.getElementById('notifications');
    const notification = document.createElement('div');
    
    notification.className = `notification ${type}`;
    notification.innerHTML = `
      <div class="notification-header">
        <span class="notification-title">${title}</span>
        <button class="notification-close">&times;</button>
      </div>
      <div class="notification-message">${message}</div>
    `;
    
    container.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      notification.remove();
    }, 5000);
    
    // Manual close
    notification.querySelector('.notification-close').addEventListener('click', () => {
      notification.remove();
    });
  }

  formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
  }

  cleanup() {
    // Cleanup resources before app closes
    if (this.grids) {
      Object.values(this.grids).forEach(grid => {
        if (grid.destroy) grid.destroy();
      });
    }
  }

  // Placeholder methods for features to be implemented
  async newProject() { console.log('New project'); }
  async openProject(filePath) { console.log('Open project:', filePath); }
  async saveProject() { console.log('Save project'); }
  async saveProjectAs(filePath) { console.log('Save project as:', filePath); }
  async importExcel(filePath) { console.log('Import Excel:', filePath); }
  async exportExcel() { console.log('Export Excel'); }
  async syncDatabase() { console.log('Sync database'); }
  async backupDatabase() { console.log('Backup database'); }
  async restoreDatabase() { console.log('Restore database'); }
  async addProduct() { console.log('Add product'); }
  async newEstimate() { console.log('New estimate'); }
  filterProducts(query) { console.log('Filter products:', query); }
  showCategoryProducts(category) { console.log('Show category:', category); }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.fireToolApp = new FireToolApp();
});
