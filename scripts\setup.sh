#!/bin/bash

echo "========================================"
echo "FireTool Setup Script for macOS/Linux"
echo "========================================"
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed"
    echo "Please download and install Node.js from https://nodejs.org/"
    echo "Recommended version: Node.js 18 LTS or higher"
    exit 1
fi

# Display Node.js version
echo "Node.js version:"
node --version
echo

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "ERROR: npm is not available"
    echo "Please reinstall Node.js from https://nodejs.org/"
    exit 1
fi

# Display npm version
echo "npm version:"
npm --version
echo

# Navigate to project directory
cd "$(dirname "$0")/.."

# Install dependencies
echo "Installing dependencies..."
echo "This may take a few minutes..."
echo

npm install

if [ $? -ne 0 ]; then
    echo
    echo "ERROR: Failed to install dependencies"
    echo
    echo "Common solutions:"
    echo "1. Install Xcode Command Line Tools: xcode-select --install (macOS)"
    echo "2. Install build-essential: sudo apt-get install build-essential (Linux)"
    echo "3. Check your internet connection"
    echo "4. Clear npm cache: npm cache clean --force"
    echo "5. Try with sudo: sudo npm install"
    echo
    exit 1
fi

# Create assets directory if it doesn't exist
if [ ! -d "assets" ]; then
    echo "Creating assets directory..."
    mkdir -p assets
fi

# Check if basic assets exist
if [ ! -f "assets/icon.png" ]; then
    echo
    echo "WARNING: Missing application icon (assets/icon.png)"
    echo "You'll need to add icon files for the application to work properly."
    echo "See assets/README.md for requirements."
    echo
fi

echo
echo "========================================"
echo "Setup completed successfully!"
echo "========================================"
echo
echo "To start the application:"
echo "  npm run dev    (development mode)"
echo "  npm start      (production mode)"
echo
echo "To build for distribution:"
echo "  npm run build"
echo
echo "For more information, see SETUP.md"
echo
