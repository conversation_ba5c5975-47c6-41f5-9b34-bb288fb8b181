const { createClient } = require('@supabase/supabase-js');

class SupabaseSync {
  constructor(databaseManager) {
    this.db = databaseManager;
    this.supabase = null;
    this.isConnected = false;
    this.syncInProgress = false;
    this.lastSyncTime = null;
    
    // Configuration - these should be loaded from settings
    this.supabaseUrl = process.env.SUPABASE_URL || '';
    this.supabaseKey = process.env.SUPABASE_ANON_KEY || '';
    
    this.syncTables = [
      'products',
      'categories',
      'suppliers',
      'labor_rates'
    ];
  }

  async initialize(url, key) {
    try {
      this.supabaseUrl = url;
      this.supabaseKey = key;
      
      this.supabase = createClient(url, key);
      
      // Test connection
      const { data, error } = await this.supabase.from('products').select('count').limit(1);
      
      if (error) {
        throw error;
      }
      
      this.isConnected = true;
      console.log('Supabase connection established');
      
      return { success: true };
    } catch (error) {
      console.error('Failed to initialize Supabase:', error);
      this.isConnected = false;
      throw error;
    }
  }

  async syncToCloud() {
    if (!this.isConnected || this.syncInProgress) {
      throw new Error('Sync not available');
    }

    this.syncInProgress = true;
    const syncResults = {
      uploaded: 0,
      failed: 0,
      errors: []
    };

    try {
      for (const tableName of this.syncTables) {
        try {
          const result = await this.uploadTable(tableName);
          syncResults.uploaded += result.uploaded;
          syncResults.failed += result.failed;
          if (result.errors) {
            syncResults.errors.push(...result.errors);
          }
        } catch (error) {
          console.error(`Failed to sync table ${tableName}:`, error);
          syncResults.errors.push(`${tableName}: ${error.message}`);
        }
      }

      this.lastSyncTime = new Date().toISOString();
      this.updateSyncStatus('completed');
      
      return syncResults;
    } catch (error) {
      console.error('Sync to cloud failed:', error);
      this.updateSyncStatus('failed');
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  async syncFromCloud() {
    if (!this.isConnected || this.syncInProgress) {
      throw new Error('Sync not available');
    }

    this.syncInProgress = true;
    const syncResults = {
      downloaded: 0,
      failed: 0,
      errors: []
    };

    try {
      for (const tableName of this.syncTables) {
        try {
          const result = await this.downloadTable(tableName);
          syncResults.downloaded += result.downloaded;
          syncResults.failed += result.failed;
          if (result.errors) {
            syncResults.errors.push(...result.errors);
          }
        } catch (error) {
          console.error(`Failed to download table ${tableName}:`, error);
          syncResults.errors.push(`${tableName}: ${error.message}`);
        }
      }

      this.lastSyncTime = new Date().toISOString();
      this.updateSyncStatus('completed');
      
      return syncResults;
    } catch (error) {
      console.error('Sync from cloud failed:', error);
      this.updateSyncStatus('failed');
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  async uploadTable(tableName) {
    const result = {
      uploaded: 0,
      failed: 0,
      errors: []
    };

    try {
      // Get local data that needs to be synced
      const localData = this.getUnsyncedData(tableName);
      
      if (localData.length === 0) {
        return result;
      }

      // Batch upload data
      const batchSize = 100;
      for (let i = 0; i < localData.length; i += batchSize) {
        const batch = localData.slice(i, i + batchSize);
        
        try {
          const { data, error } = await this.supabase
            .from(tableName)
            .upsert(batch, { onConflict: 'id' });

          if (error) {
            throw error;
          }

          // Mark as synced
          batch.forEach(item => {
            this.markAsSynced(tableName, item.id);
          });

          result.uploaded += batch.length;
        } catch (error) {
          console.error(`Failed to upload batch for ${tableName}:`, error);
          result.failed += batch.length;
          result.errors.push(`Batch upload failed: ${error.message}`);
        }
      }

      return result;
    } catch (error) {
      console.error(`Failed to upload table ${tableName}:`, error);
      throw error;
    }
  }

  async downloadTable(tableName) {
    const result = {
      downloaded: 0,
      failed: 0,
      errors: []
    };

    try {
      // Get last sync time for this table
      const lastSync = this.getLastSyncTime(tableName);
      
      let query = this.supabase.from(tableName).select('*');
      
      if (lastSync) {
        query = query.gt('updated_at', lastSync);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      if (!data || data.length === 0) {
        return result;
      }

      // Process downloaded data
      for (const item of data) {
        try {
          // Check if item exists locally
          const existingItem = this.db.findById(tableName, item.id);
          
          if (existingItem) {
            // Update existing item
            this.db.update(tableName, item.id, item);
          } else {
            // Insert new item
            this.db.insert(tableName, item);
          }

          result.downloaded++;
        } catch (error) {
          console.error(`Failed to process item ${item.id}:`, error);
          result.failed++;
          result.errors.push(`Item ${item.id}: ${error.message}`);
        }
      }

      // Update last sync time
      this.setLastSyncTime(tableName, new Date().toISOString());

      return result;
    } catch (error) {
      console.error(`Failed to download table ${tableName}:`, error);
      throw error;
    }
  }

  getUnsyncedData(tableName) {
    try {
      // Get items that haven't been synced or have been modified since last sync
      const syncLog = this.db.findAll('sync_log', {
        table_name: tableName,
        sync_status: 'pending'
      });

      const unsyncedIds = syncLog.map(log => log.record_id);
      
      if (unsyncedIds.length === 0) {
        return [];
      }

      // Get the actual data
      const allData = this.db.findAll(tableName);
      return allData.filter(item => unsyncedIds.includes(item.id));
    } catch (error) {
      console.error(`Failed to get unsynced data for ${tableName}:`, error);
      return [];
    }
  }

  markAsSynced(tableName, recordId) {
    try {
      // Update sync log
      const existingLog = this.db.findAll('sync_log', {
        table_name: tableName,
        record_id: recordId
      })[0];

      if (existingLog) {
        this.db.update('sync_log', existingLog.id, {
          sync_status: 'completed',
          synced_at: new Date().toISOString(),
          error_message: null
        });
      }
    } catch (error) {
      console.error(`Failed to mark as synced: ${tableName}/${recordId}`, error);
    }
  }

  logSyncOperation(tableName, recordId, operation) {
    try {
      this.db.insert('sync_log', {
        id: this.generateId(),
        table_name: tableName,
        operation: operation,
        record_id: recordId,
        sync_status: 'pending',
        created_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to log sync operation:', error);
    }
  }

  getLastSyncTime(tableName) {
    try {
      const setting = this.db.findAll('settings', { key: `last_sync_${tableName}` })[0];
      return setting ? setting.value : null;
    } catch (error) {
      console.error(`Failed to get last sync time for ${tableName}:`, error);
      return null;
    }
  }

  setLastSyncTime(tableName, timestamp) {
    try {
      const key = `last_sync_${tableName}`;
      const existing = this.db.findAll('settings', { key })[0];

      if (existing) {
        this.db.update('settings', existing.key, {
          value: timestamp,
          updated_at: new Date().toISOString()
        });
      } else {
        this.db.insert('settings', {
          key,
          value: timestamp,
          description: `Last sync time for ${tableName}`,
          updated_at: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error(`Failed to set last sync time for ${tableName}:`, error);
    }
  }

  updateSyncStatus(status) {
    try {
      const key = 'sync_status';
      const existing = this.db.findAll('settings', { key })[0];
      const value = JSON.stringify({
        status,
        last_sync: this.lastSyncTime,
        updated_at: new Date().toISOString()
      });

      if (existing) {
        this.db.update('settings', existing.key, {
          value,
          updated_at: new Date().toISOString()
        });
      } else {
        this.db.insert('settings', {
          key,
          value,
          description: 'Sync status information',
          updated_at: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Failed to update sync status:', error);
    }
  }

  async testConnection() {
    if (!this.supabase) {
      return { connected: false, error: 'Not initialized' };
    }

    try {
      const { data, error } = await this.supabase
        .from('products')
        .select('count')
        .limit(1);

      if (error) {
        return { connected: false, error: error.message };
      }

      return { connected: true };
    } catch (error) {
      return { connected: false, error: error.message };
    }
  }

  getSyncStatus() {
    return {
      isConnected: this.isConnected,
      syncInProgress: this.syncInProgress,
      lastSyncTime: this.lastSyncTime,
      supabaseUrl: this.supabaseUrl ? this.supabaseUrl.replace(/\/.*/, '/***') : null
    };
  }

  async configurePricing(pricingData) {
    if (!this.isConnected) {
      throw new Error('Not connected to Supabase');
    }

    try {
      // Upload pricing configuration
      const { data, error } = await this.supabase
        .from('pricing_config')
        .upsert(pricingData);

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to configure pricing:', error);
      throw error;
    }
  }

  generateId() {
    return 'sync_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  disconnect() {
    this.supabase = null;
    this.isConnected = false;
    this.syncInProgress = false;
  }
}

module.exports = SupabaseSync;
