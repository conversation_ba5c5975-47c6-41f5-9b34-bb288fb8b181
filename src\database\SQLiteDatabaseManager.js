const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');
const { v4: uuidv4 } = require('uuid');

class SQLiteDatabaseManager {
    constructor() {
        this.db = null;
        this.dbPath = null;
        this.isInitialized = false;
    }

    async initialize() {
        try {
            // Get user data directory
            const userDataPath = app.getPath('userData');
            this.dbPath = path.join(userDataPath, 'firetool.db');
            
            // Ensure directory exists
            const dbDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
            }

            // Initialize database
            this.db = new Database(this.dbPath);
            
            // Enable WAL mode for better performance
            this.db.pragma('journal_mode = WAL');
            this.db.pragma('synchronous = NORMAL');
            this.db.pragma('cache_size = 1000000');
            this.db.pragma('temp_store = memory');
            
            // Create tables
            await this.createTables();
            
            this.isInitialized = true;
            console.log('✅ SQLite Database initialized successfully at:', this.dbPath);
            
            return true;
        } catch (error) {
            console.error('❌ SQLite Database initialization failed:', error);
            throw error;
        }
    }

    createTables() {
        // Create all necessary tables for FireTool
        const tables = [
            // Projects table
            `CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                client_name TEXT,
                client_contact TEXT,
                project_type TEXT,
                location TEXT,
                status TEXT DEFAULT 'draft',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                total_cost REAL DEFAULT 0,
                markup_percentage REAL DEFAULT 15,
                tax_rate REAL DEFAULT 10,
                currency TEXT DEFAULT 'USD'
            )`,
            
            // Categories table for organizing products
            `CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                parent_id INTEGER,
                sort_order INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES categories(id)
            )`,
            
            // Products/Items table
            `CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                category_id INTEGER,
                unit TEXT DEFAULT 'pcs',
                unit_price REAL DEFAULT 0,
                labor_cost REAL DEFAULT 0,
                material_cost REAL DEFAULT 0,
                vendor TEXT,
                model_number TEXT,
                specifications TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )`,
            
            // Project items (BOQ items)
            `CREATE TABLE IF NOT EXISTS project_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                project_id INTEGER NOT NULL,
                product_id INTEGER,
                custom_name TEXT,
                custom_description TEXT,
                quantity REAL NOT NULL DEFAULT 1,
                unit_price REAL NOT NULL DEFAULT 0,
                labor_cost REAL DEFAULT 0,
                material_cost REAL DEFAULT 0,
                total_cost REAL DEFAULT 0,
                sort_order INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )`,
            
            // Fire Alarm Systems
            `CREATE TABLE IF NOT EXISTS fire_alarm_systems (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                product_id INTEGER NOT NULL,
                system_type TEXT,
                zones INTEGER DEFAULT 1,
                detection_type TEXT,
                certification TEXT,
                operating_voltage TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
            )`,
            
            // Firefighting Systems
            `CREATE TABLE IF NOT EXISTS firefighting_systems (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                product_id INTEGER NOT NULL,
                system_type TEXT,
                pressure_rating TEXT,
                flow_rate REAL,
                coverage_area REAL,
                agent_type TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
            )`,
            
            // Clean Agent Systems
            `CREATE TABLE IF NOT EXISTS clean_agent_systems (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                product_id INTEGER NOT NULL,
                agent_type TEXT,
                concentration REAL,
                discharge_time INTEGER,
                room_volume REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
            )`,
            
            // Pumps (Fire & Foam)
            `CREATE TABLE IF NOT EXISTS pumps (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                product_id INTEGER NOT NULL,
                pump_type TEXT,
                flow_rate REAL,
                pressure REAL,
                power_rating REAL,
                motor_type TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
            )`,
            
            // Civil Works
            `CREATE TABLE IF NOT EXISTS civil_works (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                product_id INTEGER NOT NULL,
                work_type TEXT,
                unit_of_measure TEXT,
                labor_hours REAL,
                material_requirements TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
            )`,
            
            // Vendors table
            `CREATE TABLE IF NOT EXISTS vendors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                contact_person TEXT,
                email TEXT,
                phone TEXT,
                address TEXT,
                website TEXT,
                rating INTEGER DEFAULT 5,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Price history for tracking changes
            `CREATE TABLE IF NOT EXISTS price_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                old_price REAL,
                new_price REAL,
                change_reason TEXT,
                changed_by TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
            )`,
            
            // Settings table
            `CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Sync log for Supabase integration
            `CREATE TABLE IF NOT EXISTS sync_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                record_uuid TEXT NOT NULL,
                action TEXT NOT NULL,
                synced BOOLEAN DEFAULT FALSE,
                sync_attempts INTEGER DEFAULT 0,
                last_sync_attempt DATETIME,
                error_message TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        // Execute table creation
        tables.forEach(sql => {
            this.db.exec(sql);
        });

        // Create indexes for better performance
        this.createIndexes();
        
        // Insert default data
        this.insertDefaultData();
    }

    createIndexes() {
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_projects_uuid ON projects(uuid)',
            'CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status)',
            'CREATE INDEX IF NOT EXISTS idx_products_uuid ON products(uuid)',
            'CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)',
            'CREATE INDEX IF NOT EXISTS idx_project_items_project ON project_items(project_id)',
            'CREATE INDEX IF NOT EXISTS idx_project_items_product ON project_items(product_id)',
            'CREATE INDEX IF NOT EXISTS idx_categories_parent ON categories(parent_id)',
            'CREATE INDEX IF NOT EXISTS idx_sync_log_synced ON sync_log(synced)',
            'CREATE INDEX IF NOT EXISTS idx_price_history_product ON price_history(product_id)'
        ];

        indexes.forEach(sql => {
            this.db.exec(sql);
        });
    }

    insertDefaultData() {
        // Insert default categories
        const defaultCategories = [
            { name: 'Fire Alarm Systems', description: 'Smoke detectors, heat detectors, manual call points, control panels' },
            { name: 'Firefighting Systems', description: 'Sprinkler systems, foam systems, water mist systems' },
            { name: 'Clean Agent Systems', description: 'FM200, Novec 1230, CO2 suppression systems' },
            { name: 'Foam Systems', description: 'Foam generators, foam tanks, foam proportioning systems' },
            { name: 'CO2 Systems', description: 'CO2 cylinders, discharge nozzles, control systems' },
            { name: 'Fire Pumps', description: 'Electric pumps, diesel pumps, jockey pumps' },
            { name: 'Foam Pumps', description: 'Foam pumps and proportioning equipment' },
            { name: 'Civil Works', description: 'Excavation, concrete work, piping, electrical installation' }
        ];

        const insertCategory = this.db.prepare(`
            INSERT OR IGNORE INTO categories (uuid, name, description)
            VALUES (?, ?, ?)
        `);

        defaultCategories.forEach((category) => {
            const uuid = uuidv4();
            insertCategory.run(uuid, category.name, category.description);
        });

        // Insert default settings
        const defaultSettings = [
            { key: 'currency', value: 'USD', description: 'Default currency for pricing' },
            { key: 'tax_rate', value: '10', description: 'Default tax rate percentage' },
            { key: 'markup_percentage', value: '15', description: 'Default markup percentage' },
            { key: 'company_name', value: 'FireTool Company', description: 'Company name for reports' },
            { key: 'company_address', value: '', description: 'Company address for reports' },
            { key: 'company_phone', value: '', description: 'Company phone for reports' },
            { key: 'company_email', value: '', description: 'Company email for reports' }
        ];

        const insertSetting = this.db.prepare(`
            INSERT OR IGNORE INTO settings (key, value, description)
            VALUES (?, ?, ?)
        `);

        defaultSettings.forEach(setting => {
            insertSetting.run(setting.key, setting.value, setting.description);
        });
    }

    // Basic CRUD operations
    insert(table, data) {
        try {
            const uuid = data.uuid || uuidv4();
            const columns = Object.keys(data).filter(key => key !== 'id');
            const placeholders = columns.map(() => '?').join(', ');
            const values = columns.map(col => data[col]);
            
            // Add UUID if not present
            if (!data.uuid) {
                columns.push('uuid');
                values.push(uuid);
            }
            
            const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;
            const stmt = this.db.prepare(sql);
            const result = stmt.run(...values);
            
            return { id: result.lastInsertRowid, uuid, changes: result.changes };
        } catch (error) {
            console.error(`Error inserting into ${table}:`, error);
            throw error;
        }
    }

    update(table, id, data) {
        try {
            const columns = Object.keys(data).filter(key => key !== 'id');
            const setClause = columns.map(col => `${col} = ?`).join(', ');
            const values = columns.map(col => data[col]);
            values.push(id);
            
            const sql = `UPDATE ${table} SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
            const stmt = this.db.prepare(sql);
            const result = stmt.run(...values);
            
            return { changes: result.changes };
        } catch (error) {
            console.error(`Error updating ${table}:`, error);
            throw error;
        }
    }

    delete(table, id) {
        try {
            const sql = `DELETE FROM ${table} WHERE id = ?`;
            const stmt = this.db.prepare(sql);
            const result = stmt.run(id);
            
            return { changes: result.changes };
        } catch (error) {
            console.error(`Error deleting from ${table}:`, error);
            throw error;
        }
    }

    findById(table, id) {
        try {
            const sql = `SELECT * FROM ${table} WHERE id = ?`;
            const stmt = this.db.prepare(sql);
            return stmt.get(id);
        } catch (error) {
            console.error(`Error finding by ID in ${table}:`, error);
            throw error;
        }
    }

    findAll(table, conditions = {}) {
        try {
            let sql = `SELECT * FROM ${table}`;
            const values = [];
            
            if (Object.keys(conditions).length > 0) {
                const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
                sql += ` WHERE ${whereClause}`;
                values.push(...Object.values(conditions));
            }
            
            const stmt = this.db.prepare(sql);
            return stmt.all(...values);
        } catch (error) {
            console.error(`Error finding all in ${table}:`, error);
            throw error;
        }
    }

    // Get database path for backup purposes
    getDatabasePath() {
        return this.dbPath;
    }

    // Close database connection
    close() {
        if (this.db) {
            this.db.close();
            this.isInitialized = false;
        }
    }
}

module.exports = SQLiteDatabaseManager;
