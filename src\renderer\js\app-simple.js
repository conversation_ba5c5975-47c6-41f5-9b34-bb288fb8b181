const { ipc<PERSON>enderer } = require('electron');

class FireToolApp {
    constructor() {
        this.currentView = 'dashboard';
        this.projects = [];
        this.items = [];
        this.categories = [];
        this.settings = {};
        
        this.init();
    }
    
    async init() {
        try {
            // Load initial data
            await this.loadData();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Render initial view
            this.renderDashboard();
            
            console.log('🔥 FireTool App initialized successfully!');
        } catch (error) {
            console.error('Error initializing app:', error);
            this.showError('Failed to initialize application');
        }
    }
    
    async loadData() {
        try {
            this.projects = await ipcRenderer.invoke('get-projects');
            this.items = await ipcRenderer.invoke('get-items');
            this.categories = await ipcRenderer.invoke('get-categories');
            this.settings = await ipcRenderer.invoke('get-settings');
        } catch (error) {
            console.error('Error loading data:', error);
            throw error;
        }
    }
    
    setupEventListeners() {
        // Navigation
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-nav]')) {
                e.preventDefault();
                const view = e.target.getAttribute('data-nav');
                this.navigateTo(view);
            }
            
            if (e.target.matches('[data-action]')) {
                e.preventDefault();
                const action = e.target.getAttribute('data-action');
                this.handleAction(action, e.target);
            }
        });
        
        // Form submissions
        document.addEventListener('submit', (e) => {
            e.preventDefault();
            const form = e.target;
            const action = form.getAttribute('data-form-action');
            if (action) {
                this.handleFormSubmit(action, form);
            }
        });
    }
    
    navigateTo(view) {
        this.currentView = view;
        
        // Update navigation
        document.querySelectorAll('[data-nav]').forEach(nav => {
            nav.classList.remove('active');
        });
        document.querySelector(`[data-nav="${view}"]`)?.classList.add('active');
        
        // Render view
        switch (view) {
            case 'dashboard':
                this.renderDashboard();
                break;
            case 'projects':
                this.renderProjects();
                break;
            case 'items':
                this.renderItems();
                break;
            case 'settings':
                this.renderSettings();
                break;
            default:
                this.renderDashboard();
        }
    }
    
    renderDashboard() {
        const content = document.getElementById('main-content');
        content.innerHTML = `
            <div class="dashboard">
                <h1>🔥 FireTool Dashboard</h1>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Projects</h3>
                        <div class="stat-number">${this.projects.length}</div>
                        <p>Total projects</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>Items</h3>
                        <div class="stat-number">${this.items.length}</div>
                        <p>Catalog items</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>Categories</h3>
                        <div class="stat-number">${this.categories.length}</div>
                        <p>Item categories</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>Recent Activity</h3>
                        <div class="stat-number">${this.getRecentProjectsCount()}</div>
                        <p>Projects this month</p>
                    </div>
                </div>
                
                <div class="quick-actions">
                    <h2>Quick Actions</h2>
                    <div class="action-buttons">
                        <button class="btn btn-primary" data-action="new-project">
                            📋 New Project
                        </button>
                        <button class="btn btn-secondary" data-action="new-item">
                            📦 Add Item
                        </button>
                        <button class="btn btn-secondary" data-nav="projects">
                            📊 View Projects
                        </button>
                    </div>
                </div>
                
                <div class="recent-projects">
                    <h2>Recent Projects</h2>
                    ${this.renderRecentProjects()}
                </div>
            </div>
        `;
    }
    
    renderProjects() {
        const content = document.getElementById('main-content');
        content.innerHTML = `
            <div class="projects-view">
                <div class="view-header">
                    <h1>📋 Projects</h1>
                    <button class="btn btn-primary" data-action="new-project">
                        ➕ New Project
                    </button>
                </div>
                
                <div class="projects-list">
                    ${this.projects.length === 0 ? 
                        '<div class="empty-state">No projects yet. Create your first project!</div>' :
                        this.projects.map(project => this.renderProjectCard(project)).join('')
                    }
                </div>
            </div>
        `;
    }
    
    renderItems() {
        const content = document.getElementById('main-content');
        content.innerHTML = `
            <div class="items-view">
                <div class="view-header">
                    <h1>📦 Items Catalog</h1>
                    <button class="btn btn-primary" data-action="new-item">
                        ➕ Add Item
                    </button>
                </div>
                
                <div class="items-grid">
                    ${this.items.length === 0 ? 
                        '<div class="empty-state">No items in catalog. Add your first item!</div>' :
                        this.items.map(item => this.renderItemCard(item)).join('')
                    }
                </div>
            </div>
        `;
    }
    
    renderSettings() {
        const content = document.getElementById('main-content');
        content.innerHTML = `
            <div class="settings-view">
                <h1>⚙️ Settings</h1>
                
                <form data-form-action="update-settings" class="settings-form">
                    <div class="form-group">
                        <label for="currency">Currency</label>
                        <select id="currency" name="currency" value="${this.settings.currency || 'USD'}">
                            <option value="USD">USD ($)</option>
                            <option value="EUR">EUR (€)</option>
                            <option value="GBP">GBP (£)</option>
                            <option value="CAD">CAD (C$)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="taxRate">Tax Rate (%)</label>
                        <input type="number" id="taxRate" name="taxRate" 
                               value="${(this.settings.taxRate || 0.1) * 100}" 
                               min="0" max="100" step="0.1">
                    </div>
                    
                    <div class="form-group">
                        <label for="profitMargin">Profit Margin (%)</label>
                        <input type="number" id="profitMargin" name="profitMargin" 
                               value="${(this.settings.profitMargin || 0.15) * 100}" 
                               min="0" max="100" step="0.1">
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Save Settings</button>
                        <button type="button" class="btn btn-secondary" data-action="export-data">
                            Export Data
                        </button>
                        <button type="button" class="btn btn-secondary" data-action="import-data">
                            Import Data
                        </button>
                    </div>
                </form>
                
                <div class="data-info">
                    <h3>Data Storage</h3>
                    <p>Data is stored locally on your computer.</p>
                    <p><strong>Last backup:</strong> ${this.settings.lastBackup ? 
                        new Date(this.settings.lastBackup).toLocaleString() : 'Never'}</p>
                </div>
            </div>
        `;
        
        // Set select value
        setTimeout(() => {
            const currencySelect = document.getElementById('currency');
            if (currencySelect) {
                currencySelect.value = this.settings.currency || 'USD';
            }
        }, 0);
    }
    
    renderProjectCard(project) {
        return `
            <div class="project-card">
                <h3>${project.name || 'Untitled Project'}</h3>
                <p>${project.description || 'No description'}</p>
                <div class="project-meta">
                    <span>Created: ${new Date(project.createdAt).toLocaleDateString()}</span>
                    <span>Items: ${project.items?.length || 0}</span>
                </div>
                <div class="project-actions">
                    <button class="btn btn-sm" data-action="edit-project" data-id="${project.id}">
                        ✏️ Edit
                    </button>
                    <button class="btn btn-sm btn-danger" data-action="delete-project" data-id="${project.id}">
                        🗑️ Delete
                    </button>
                </div>
            </div>
        `;
    }
    
    renderItemCard(item) {
        return `
            <div class="item-card">
                <h4>${item.name || 'Untitled Item'}</h4>
                <p>${item.description || 'No description'}</p>
                <div class="item-price">$${item.price || '0.00'}</div>
                <div class="item-category">${this.getCategoryName(item.categoryId)}</div>
                <div class="item-actions">
                    <button class="btn btn-sm" data-action="edit-item" data-id="${item.id}">
                        ✏️ Edit
                    </button>
                    <button class="btn btn-sm btn-danger" data-action="delete-item" data-id="${item.id}">
                        🗑️ Delete
                    </button>
                </div>
            </div>
        `;
    }
    
    renderRecentProjects() {
        const recent = this.projects
            .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
            .slice(0, 5);
            
        if (recent.length === 0) {
            return '<div class="empty-state">No recent projects</div>';
        }
        
        return recent.map(project => `
            <div class="recent-project">
                <h4>${project.name}</h4>
                <span>${new Date(project.updatedAt).toLocaleDateString()}</span>
            </div>
        `).join('');
    }
    
    getCategoryName(categoryId) {
        const category = this.categories.find(c => c.id === categoryId);
        return category ? category.name : 'Uncategorized';
    }
    
    getRecentProjectsCount() {
        const oneMonthAgo = new Date();
        oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
        
        return this.projects.filter(project => 
            new Date(project.createdAt) > oneMonthAgo
        ).length;
    }
    
    async handleAction(action, element) {
        const id = element.getAttribute('data-id');
        
        try {
            switch (action) {
                case 'new-project':
                    await this.createProject();
                    break;
                case 'new-item':
                    await this.createItem();
                    break;
                case 'edit-project':
                    await this.editProject(parseInt(id));
                    break;
                case 'delete-project':
                    await this.deleteProject(parseInt(id));
                    break;
                case 'edit-item':
                    await this.editItem(parseInt(id));
                    break;
                case 'delete-item':
                    await this.deleteItem(parseInt(id));
                    break;
                case 'export-data':
                    await this.exportData();
                    break;
                case 'import-data':
                    await this.importData();
                    break;
            }
        } catch (error) {
            console.error('Error handling action:', error);
            this.showError('Action failed: ' + error.message);
        }
    }
    
    async handleFormSubmit(action, form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        try {
            switch (action) {
                case 'update-settings':
                    await this.updateSettings(data);
                    break;
            }
        } catch (error) {
            console.error('Error handling form submit:', error);
            this.showError('Form submission failed: ' + error.message);
        }
    }
    
    async createProject() {
        const name = prompt('Project name:');
        if (!name) return;
        
        const description = prompt('Project description (optional):') || '';
        
        const project = await ipcRenderer.invoke('add-project', {
            name,
            description,
            items: []
        });
        
        this.projects.push(project);
        this.navigateTo('projects');
        this.showSuccess('Project created successfully!');
    }
    
    async createItem() {
        const name = prompt('Item name:');
        if (!name) return;
        
        const description = prompt('Item description (optional):') || '';
        const price = parseFloat(prompt('Item price:') || '0');
        
        const item = await ipcRenderer.invoke('add-item', {
            name,
            description,
            price,
            categoryId: this.categories[0]?.id || 1
        });
        
        this.items.push(item);
        this.navigateTo('items');
        this.showSuccess('Item added successfully!');
    }
    
    async deleteProject(id) {
        if (!confirm('Are you sure you want to delete this project?')) return;
        
        await ipcRenderer.invoke('delete-project', id);
        this.projects = this.projects.filter(p => p.id !== id);
        this.navigateTo('projects');
        this.showSuccess('Project deleted successfully!');
    }
    
    async deleteItem(id) {
        if (!confirm('Are you sure you want to delete this item?')) return;
        
        await ipcRenderer.invoke('delete-item', id);
        this.items = this.items.filter(i => i.id !== id);
        this.navigateTo('items');
        this.showSuccess('Item deleted successfully!');
    }
    
    async updateSettings(data) {
        const settings = {
            currency: data.currency,
            taxRate: parseFloat(data.taxRate) / 100,
            profitMargin: parseFloat(data.profitMargin) / 100
        };
        
        this.settings = await ipcRenderer.invoke('update-settings', settings);
        this.showSuccess('Settings updated successfully!');
    }
    
    async exportData() {
        const data = await ipcRenderer.invoke('export-data');
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `firetool-backup-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        this.showSuccess('Data exported successfully!');
    }
    
    async importData() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            try {
                const text = await file.text();
                const success = await ipcRenderer.invoke('import-data', text);
                
                if (success) {
                    await this.loadData();
                    this.navigateTo('dashboard');
                    this.showSuccess('Data imported successfully!');
                } else {
                    this.showError('Failed to import data. Please check the file format.');
                }
            } catch (error) {
                this.showError('Error reading file: ' + error.message);
            }
        };
        
        input.click();
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'error');
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.fireToolApp = new FireToolApp();
});

// Export for debugging
window.FireToolApp = FireToolApp;
