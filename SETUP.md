# FireTool Setup Guide

This guide will help you set up and run the FireTool desktop application on your system.

## Prerequisites

### 1. Install Node.js
Download and install Node.js from [nodejs.org](https://nodejs.org/)
- **Recommended version**: Node.js 18 LTS or higher
- **Includes**: npm package manager

### 2. Verify Installation
Open a terminal/command prompt and run:
```bash
node --version
npm --version
```

Both commands should return version numbers.

## Installation Steps

### 1. Navigate to Project Directory
```bash
cd "d:\Firetool Beta"
```

### 2. Install Dependencies
```bash
npm install
```

This will install all required packages including:
- Electron framework
- Database libraries (better-sqlite3)
- Excel processing (exceljs)
- UI components (ag-grid)
- And many more...

### 3. Create Placeholder Assets
Create basic icon files in the `assets` directory:

**For Windows development**, create a simple icon:
```bash
# Create assets directory if it doesn't exist
mkdir assets

# You'll need to manually create or download:
# - assets/icon.png (256x256)
# - assets/icon.ico (Windows icon)
# - assets/icon.icns (macOS icon)
# - assets/logo.png (32x32)
```

## Running the Application

### Development Mode
```bash
npm run dev
```
This starts the application in development mode with:
- Hot reload enabled
- Developer tools available
- Debug logging

### Production Mode
```bash
npm start
```
This runs the application in production mode.

## Building for Distribution

### Build for Current Platform
```bash
npm run build
```

### Build for Specific Platforms
```bash
npm run build-win     # Windows installer
npm run build-mac     # macOS DMG
npm run build-linux   # Linux AppImage
```

Built applications will be in the `dist` directory.

## First Run Setup

### 1. Database Initialization
On first run, FireTool will:
- Create a local SQLite database
- Set up default categories for fire safety equipment
- Create necessary tables and indexes

### 2. Import Sample Data
To get started quickly:
1. Use **File > Import Excel** to import your existing product catalogs
2. Or manually add products through the **Products** view
3. Organize products into appropriate categories

### 3. Create Your First BOQ
1. Go to **BOQ Builder**
2. Drag products from the catalog to your BOQ
3. Edit quantities and prices as needed
4. Save your BOQ for future use

## Troubleshooting

### Common Issues

#### 1. "npm install" fails
- **Solution**: Make sure you have Node.js 16+ installed
- **Windows**: Run as Administrator if permission errors occur
- **macOS/Linux**: Use `sudo npm install` if needed

#### 2. "better-sqlite3" compilation errors
- **Solution**: Install build tools for your platform
- **Windows**: Install Visual Studio Build Tools
- **macOS**: Install Xcode Command Line Tools: `xcode-select --install`
- **Linux**: Install build-essential: `sudo apt-get install build-essential`

#### 3. Application won't start
- **Check**: Node.js version compatibility
- **Check**: All dependencies installed successfully
- **Try**: Delete `node_modules` and run `npm install` again

#### 4. Database errors
- **Solution**: Delete the database file and restart (data will be lost)
- **Location**: Database is stored in the user data directory
- **Windows**: `%APPDATA%/firetool/firetool.db`
- **macOS**: `~/Library/Application Support/firetool/firetool.db`
- **Linux**: `~/.config/firetool/firetool.db`

### Performance Issues

#### Large Excel Files
- **Tip**: Break large files into smaller chunks
- **Tip**: Use CSV format for better performance
- **Tip**: Import during off-peak hours

#### Slow Grid Performance
- **Solution**: Enable virtual scrolling (already configured)
- **Solution**: Reduce visible columns
- **Solution**: Use filtering to limit displayed data

## Configuration

### Environment Variables
Create a `.env` file in the project root for optional configuration:

```env
# Supabase Configuration (optional)
SUPABASE_URL=your-supabase-project-url
SUPABASE_ANON_KEY=your-supabase-anon-key

# Application Settings
NODE_ENV=development
DEBUG=true
```

### Application Settings
Settings are stored locally and include:
- Default currency (USD, EUR, GBP, etc.)
- Regional pricing preferences
- Grid display options
- Auto-save intervals
- Backup schedules

## Data Management

### Backup Strategy
FireTool automatically creates backups:
- **Location**: `userData/backups/`
- **Frequency**: Daily (configurable)
- **Retention**: 30 days (configurable)

### Manual Backup
1. **File > Backup Database** - Creates immediate backup
2. **Copy project files** - Save `.firetool` project files
3. **Export data** - Use Excel export for external backup

### Data Recovery
1. **File > Restore Database** - Restore from backup
2. **Import Excel** - Restore from exported data
3. **Project files** - Open saved `.firetool` files

## Security Considerations

### Local Data
- Database is stored locally and not encrypted by default
- Consider disk encryption for sensitive data
- Regular backups to secure locations

### Cloud Sync
- Supabase integration uses secure HTTPS connections
- API keys should be kept confidential
- Consider row-level security policies

## Getting Help

### Documentation
- Check the README.md for feature overview
- Review code comments for technical details
- Check GitHub issues for known problems

### Support Channels
- Create GitHub issues for bugs
- Use discussions for feature requests
- Contact development team for urgent issues

### Community
- Share templates and configurations
- Contribute improvements and fixes
- Help other users with setup issues

## Next Steps

After successful setup:

1. **Import your data** - Start with existing Excel catalogs
2. **Organize categories** - Set up your fire safety product structure
3. **Create templates** - Build reusable BOQ templates
4. **Configure pricing** - Set up markup and discount rules
5. **Train your team** - Share knowledge and best practices

## Advanced Configuration

### Custom Categories
Modify the default categories in `src/database/DatabaseManager.js`:
```javascript
const defaultCategories = [
  { name: 'Your Custom Category', description: 'Description' },
  // Add more categories as needed
];
```

### Database Schema Changes
For advanced users, you can modify the database schema:
1. Update table definitions in `DatabaseManager.js`
2. Create migration scripts for existing data
3. Test thoroughly before production use

### Custom Reports
Extend the reporting system:
1. Add new report templates
2. Customize PDF generation
3. Integrate with external systems

Remember to test all changes in development mode before deploying to production!
