// BOQ (Bill of Quantities) Management Module

class BOQManager {
  constructor() {
    this.currentBOQ = null;
    this.catalogData = [];
    this.filteredCatalog = [];
    this.selectedItems = [];
    
    this.init();
  }

  init() {
    this.initializeCatalogTree();
    this.initializeDragDrop();
    this.initializeEventListeners();
  }

  initializeCatalogTree() {
    this.loadCatalogData();
  }

  async loadCatalogData() {
    try {
      const products = await window.fireToolApp.database.getAllProducts();
      const categories = await window.fireToolApp.database.getAllCategories();
      
      this.catalogData = products;
      this.buildCatalogTree(products, categories);
    } catch (error) {
      console.error('Failed to load catalog data:', error);
    }
  }

  buildCatalogTree(products, categories) {
    const catalogContainer = document.getElementById('catalog-tree');
    if (!catalogContainer) return;

    catalogContainer.innerHTML = '';

    // Group products by category
    const productsByCategory = Utils.groupBy(products, 'category_id');

    categories.forEach(category => {
      const categoryProducts = productsByCategory[category.id] || [];
      
      if (categoryProducts.length === 0) return;

      const categoryElement = this.createCategoryElement(category, categoryProducts);
      catalogContainer.appendChild(categoryElement);
    });
  }

  createCategoryElement(category, products) {
    const categoryDiv = Utils.createElement('div', { className: 'catalog-category' });
    
    const headerDiv = Utils.createElement('div', {
      className: 'category-header',
      innerHTML: `
        <span>${category.name}</span>
        <span class="icon-expand"></span>
      `
    });

    const itemsDiv = Utils.createElement('div', { className: 'category-items' });
    
    products.forEach(product => {
      const itemElement = this.createCatalogItem(product);
      itemsDiv.appendChild(itemElement);
    });

    // Toggle functionality
    headerDiv.addEventListener('click', () => {
      const isExpanded = itemsDiv.style.display !== 'none';
      itemsDiv.style.display = isExpanded ? 'none' : 'block';
      
      const icon = headerDiv.querySelector('.icon-expand, .icon-collapse');
      icon.className = isExpanded ? 'icon-expand' : 'icon-collapse';
    });

    categoryDiv.appendChild(headerDiv);
    categoryDiv.appendChild(itemsDiv);

    return categoryDiv;
  }

  createCatalogItem(product) {
    const itemDiv = Utils.createElement('div', {
      className: 'catalog-item',
      draggable: true,
      'data-product-id': product.id
    });

    itemDiv.innerHTML = `
      <div class="item-name">${product.name}</div>
      <div class="item-price">${Utils.formatCurrency(product.base_price || 0)}</div>
      <div class="item-unit">${product.unit || 'each'}</div>
    `;

    // Add drag event listeners
    itemDiv.addEventListener('dragstart', (e) => {
      e.dataTransfer.setData('application/json', JSON.stringify(product));
      e.dataTransfer.effectAllowed = 'copy';
    });

    // Add click to add functionality
    itemDiv.addEventListener('dblclick', () => {
      this.addProductToBOQ(product);
    });

    return itemDiv;
  }

  initializeDragDrop() {
    const boqGrid = document.getElementById('boq-grid');
    if (!boqGrid) return;

    boqGrid.addEventListener('dragover', (e) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'copy';
    });

    boqGrid.addEventListener('drop', (e) => {
      e.preventDefault();
      
      try {
        const productData = JSON.parse(e.dataTransfer.getData('application/json'));
        this.addProductToBOQ(productData);
      } catch (error) {
        console.error('Failed to parse dropped data:', error);
      }
    });
  }

  initializeEventListeners() {
    // Catalog search
    const catalogSearch = document.getElementById('catalog-search');
    if (catalogSearch) {
      catalogSearch.addEventListener('input', Utils.debounce((e) => {
        this.filterCatalog(e.target.value);
      }, 300));
    }

    // BOQ title
    const boqTitle = document.getElementById('boq-title');
    if (boqTitle) {
      boqTitle.addEventListener('blur', () => {
        this.updateBOQTitle(boqTitle.value);
      });
    }

    // Save BOQ button
    const saveBOQBtn = document.getElementById('save-boq-btn');
    if (saveBOQBtn) {
      saveBOQBtn.addEventListener('click', () => {
        this.saveBOQ();
      });
    }

    // Export BOQ button
    const exportBOQBtn = document.getElementById('export-boq-btn');
    if (exportBOQBtn) {
      exportBOQBtn.addEventListener('click', () => {
        this.exportBOQ();
      });
    }
  }

  filterCatalog(searchTerm) {
    if (!searchTerm.trim()) {
      this.loadCatalogData();
      return;
    }

    const filtered = this.catalogData.filter(product => 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.code?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Rebuild catalog tree with filtered products
    window.fireToolApp.database.getAllCategories().then(categories => {
      this.buildCatalogTree(filtered, categories);
    });
  }

  addProductToBOQ(product) {
    const boqItem = {
      id: Utils.generateId('boq_item'),
      product_id: product.id,
      product_code: product.code,
      item_description: product.name,
      unit: product.unit || 'each',
      quantity: 1,
      unit_price: product.base_price || 0,
      total_price: product.base_price || 0,
      markup_percentage: 0,
      discount_percentage: 0,
      notes: ''
    };

    // Add to grid
    if (window.fireToolApp.grids.boq) {
      window.fireToolApp.grids.boq.addBOQItem(boqItem);
    }

    // Show notification
    window.fireToolApp.showNotification(
      'Item Added',
      `${product.name} added to BOQ`,
      'success'
    );
  }

  updateBOQTitle(title) {
    if (this.currentBOQ) {
      this.currentBOQ.name = title;
    }
  }

  async saveBOQ() {
    try {
      const boqTitle = document.getElementById('boq-title').value || 'Untitled BOQ';
      const boqItems = window.fireToolApp.grids.boq.data;

      if (boqItems.length === 0) {
        window.fireToolApp.showNotification(
          'Warning',
          'Cannot save empty BOQ. Please add items first.',
          'warning'
        );
        return;
      }

      let boqId;

      if (this.currentBOQ) {
        // Update existing BOQ
        await window.fireToolApp.database.update('boq', this.currentBOQ.id, {
          name: boqTitle,
          updated_at: new Date().toISOString()
        });
        boqId = this.currentBOQ.id;
      } else {
        // Create new BOQ
        const newBOQ = await window.fireToolApp.database.addBOQ({
          name: boqTitle,
          project_id: null // Will be set when project is selected
        });
        boqId = newBOQ.id;
        this.currentBOQ = newBOQ;
      }

      // Save BOQ items
      for (const item of boqItems) {
        if (item.boq_id) {
          // Update existing item
          await window.fireToolApp.database.update('boq_items', item.id, {
            ...item,
            boq_id: boqId
          });
        } else {
          // Add new item
          await window.fireToolApp.database.addBOQItem({
            ...item,
            boq_id: boqId
          });
        }
      }

      window.fireToolApp.showNotification(
        'Success',
        'BOQ saved successfully',
        'success'
      );

    } catch (error) {
      console.error('Failed to save BOQ:', error);
      window.fireToolApp.showNotification(
        'Error',
        'Failed to save BOQ',
        'error'
      );
    }
  }

  async loadBOQ(boqId) {
    try {
      const boq = await window.fireToolApp.database.getBOQById(boqId);
      const items = await window.fireToolApp.database.getBOQItems(boqId);

      this.currentBOQ = boq;

      // Update UI
      document.getElementById('boq-title').value = boq.name;
      
      if (window.fireToolApp.grids.boq) {
        window.fireToolApp.grids.boq.setData(items);
      }

    } catch (error) {
      console.error('Failed to load BOQ:', error);
      window.fireToolApp.showNotification(
        'Error',
        'Failed to load BOQ',
        'error'
      );
    }
  }

  newBOQ() {
    this.currentBOQ = null;
    document.getElementById('boq-title').value = '';
    
    if (window.fireToolApp.grids.boq) {
      window.fireToolApp.grids.boq.setData([]);
    }
  }

  exportBOQ() {
    const boqItems = window.fireToolApp.grids.boq.data;
    
    if (boqItems.length === 0) {
      window.fireToolApp.showNotification(
        'Warning',
        'No items to export',
        'warning'
      );
      return;
    }

    // Prepare data for export
    const exportData = boqItems.map(item => ({
      'Item Description': item.item_description,
      'Product Code': item.product_code,
      'Unit': item.unit,
      'Quantity': item.quantity,
      'Unit Price': item.unit_price,
      'Total Price': item.total_price,
      'Notes': item.notes
    }));

    // Add summary row
    const totalAmount = boqItems.reduce((sum, item) => sum + (item.total_price || 0), 0);
    exportData.push({
      'Item Description': 'TOTAL',
      'Product Code': '',
      'Unit': '',
      'Quantity': '',
      'Unit Price': '',
      'Total Price': totalAmount,
      'Notes': ''
    });

    const boqTitle = document.getElementById('boq-title').value || 'BOQ';
    const filename = `${Utils.slugify(boqTitle)}_${new Date().toISOString().split('T')[0]}.csv`;

    Utils.exportToCSV(exportData, filename);

    window.fireToolApp.showNotification(
      'Success',
      'BOQ exported successfully',
      'success'
    );
  }

  calculateBOQSummary() {
    const boqItems = window.fireToolApp.grids.boq.data;
    
    const summary = {
      totalItems: boqItems.length,
      totalQuantity: boqItems.reduce((sum, item) => sum + (item.quantity || 0), 0),
      subtotal: boqItems.reduce((sum, item) => sum + (item.total_price || 0), 0),
      totalMarkup: 0,
      totalDiscount: 0,
      grandTotal: 0
    };

    // Calculate markup and discount
    boqItems.forEach(item => {
      const itemTotal = item.total_price || 0;
      const markup = (itemTotal * (item.markup_percentage || 0)) / 100;
      const discount = (itemTotal * (item.discount_percentage || 0)) / 100;
      
      summary.totalMarkup += markup;
      summary.totalDiscount += discount;
    });

    summary.grandTotal = summary.subtotal + summary.totalMarkup - summary.totalDiscount;

    return summary;
  }

  generateBOQReport() {
    const summary = this.calculateBOQSummary();
    const boqTitle = document.getElementById('boq-title').value || 'BOQ Report';
    
    const reportContent = `
      <div class="boq-report">
        <h1>${boqTitle}</h1>
        <div class="report-summary">
          <h2>Summary</h2>
          <table>
            <tr><td>Total Items:</td><td>${summary.totalItems}</td></tr>
            <tr><td>Total Quantity:</td><td>${summary.totalQuantity}</td></tr>
            <tr><td>Subtotal:</td><td>${Utils.formatCurrency(summary.subtotal)}</td></tr>
            <tr><td>Total Markup:</td><td>${Utils.formatCurrency(summary.totalMarkup)}</td></tr>
            <tr><td>Total Discount:</td><td>${Utils.formatCurrency(summary.totalDiscount)}</td></tr>
            <tr><td><strong>Grand Total:</strong></td><td><strong>${Utils.formatCurrency(summary.grandTotal)}</strong></td></tr>
          </table>
        </div>
      </div>
    `;

    return reportContent;
  }
}

// Initialize BOQ manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  if (document.getElementById('boq-view')) {
    window.boqManager = new BOQManager();
  }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BOQManager;
}
