# Assets Directory

This directory contains application assets for FireTool.

## Required Assets

### Icons
- `icon.png` - Main application icon (256x256 PNG)
- `icon.ico` - Windows icon file
- `icon.icns` - macOS icon file

### Logo
- `logo.png` - Application logo for header (32x32 PNG)

### Sample Data
- `sample-products.xlsx` - Sample product catalog
- `sample-boq.xlsx` - Sample BOQ template

## Icon Requirements

### Windows (.ico)
- Multiple sizes: 16x16, 32x32, 48x48, 256x256
- 32-bit color depth with alpha channel

### macOS (.icns)
- Multiple sizes: 16x16, 32x32, 128x128, 256x256, 512x512, 1024x1024
- PNG format with transparency

### Linux (.png)
- 256x256 PNG with transparency
- Used for desktop shortcuts and window icons

## Creating Icons

You can use online tools or software like:
- [IconGenerator](https://icongenerator.net/)
- [CloudConvert](https://cloudconvert.com/)
- Adobe Photoshop/Illustrator
- GIMP (free alternative)

## Placeholder Assets

For development, you can use simple colored squares as placeholders:
- Create a 256x256 red square for the main icon
- Create a 32x32 version for the logo
- Convert to appropriate formats for each platform
