/* Component Styles for FireTool */

/* Dashboard Components */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

.dashboard-card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 20px;
  border: 1px solid var(--border-color);
}

.dashboard-card h3 {
  margin-bottom: 15px;
  font-size: var(--font-size-lg);
  color: var(--text-color);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background-color: var(--light-color);
  border-radius: var(--border-radius);
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.activity-list {
  max-height: 200px;
  overflow-y: auto;
}

.activity-item {
  padding: 10px 0;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-text {
  flex: 1;
  font-size: var(--font-size-sm);
}

.activity-time {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

/* BOQ Builder Components */
.boq-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.boq-sidebar {
  width: 300px;
  background-color: var(--light-color);
  border-right: 1px solid var(--border-color);
  padding: 20px;
  overflow-y: auto;
}

.boq-sidebar h3 {
  margin-bottom: 15px;
  font-size: var(--font-size-lg);
  color: var(--text-color);
}

.catalog-search {
  margin-bottom: 15px;
}

.catalog-search input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
}

.catalog-tree {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.catalog-category {
  margin-bottom: 10px;
}

.category-header {
  padding: 8px 12px;
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-header:hover {
  background-color: var(--primary-dark);
}

.category-items {
  padding-left: 15px;
  margin-top: 5px;
}

.catalog-item {
  padding: 8px 12px;
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: 5px;
  cursor: grab;
  transition: var(--transition);
}

.catalog-item:hover {
  background-color: var(--light-color);
  transform: translateX(5px);
}

.catalog-item:active {
  cursor: grabbing;
}

.item-name {
  font-weight: 500;
  margin-bottom: 3px;
}

.item-price {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: bold;
}

.item-unit {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.boq-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.boq-header {
  padding: 20px;
  background-color: var(--light-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.boq-title-input {
  font-size: var(--font-size-xl);
  font-weight: bold;
  border: none;
  background: transparent;
  padding: 8px;
  border-bottom: 2px solid var(--primary-color);
  flex: 1;
  margin-right: 20px;
}

.boq-info {
  display: flex;
  gap: 20px;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.boq-info span {
  white-space: nowrap;
}

/* Modal Components */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-overlay.show {
  display: flex;
}

.modal-container {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  position: relative;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-muted);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: var(--text-color);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Form Components */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-color);
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  transition: var(--transition);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

/* Notification Components */
.notifications-container {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
}

.notification {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 15px;
  margin-bottom: 10px;
  border-left: 4px solid var(--primary-color);
  animation: slideIn 0.3s ease;
}

.notification.success {
  border-left-color: var(--success-color);
}

.notification.warning {
  border-left-color: var(--warning-color);
}

.notification.error {
  border-left-color: var(--danger-color);
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.notification-title {
  font-weight: 600;
  font-size: var(--font-size-base);
}

.notification-close {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-muted);
  font-size: 18px;
}

.notification-message {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Drag and Drop */
.drop-zone {
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  padding: 40px;
  text-align: center;
  color: var(--text-muted);
  transition: var(--transition);
}

.drop-zone.drag-over {
  border-color: var(--primary-color);
  background-color: rgba(231, 76, 60, 0.1);
  color: var(--primary-color);
}

.drop-zone-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.drop-zone-text {
  font-size: var(--font-size-lg);
  margin-bottom: 10px;
}

.drop-zone-subtext {
  font-size: var(--font-size-sm);
}

/* Progress Bar */
.progress {
  width: 100%;
  height: 8px;
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

/* Icons (using text-based icons for now) */
.icon-plus::before { content: '+'; }
.icon-edit::before { content: '✏'; }
.icon-delete::before { content: '🗑'; }
.icon-save::before { content: '💾'; }
.icon-upload::before { content: '📤'; }
.icon-download::before { content: '📥'; }
.icon-search::before { content: '🔍'; }
.icon-sync::before { content: '🔄'; }
.icon-settings::before { content: '⚙'; }
.icon-close::before { content: '✕'; }
.icon-expand::before { content: '▼'; }
.icon-collapse::before { content: '▶'; }
