const fs = require('fs');
const path = require('path');
const { app } = require('electron');

class DataManager {
    constructor() {
        // Get user data directory
        this.dataDir = app.getPath('userData');
        this.dataFile = path.join(this.dataDir, 'firetool-data.json');
        this.backupFile = path.join(this.dataDir, 'firetool-data-backup.json');
        
        // Ensure data directory exists
        if (!fs.existsSync(this.dataDir)) {
            fs.mkdirSync(this.dataDir, { recursive: true });
        }
        
        // Initialize data structure
        this.defaultData = {
            projects: [],
            items: [],
            categories: [
                { id: 1, name: 'Fire Detection Systems', description: 'Smoke detectors, heat detectors, manual call points' },
                { id: 2, name: 'Fire Suppression Systems', description: 'Sprinkler systems, foam systems, gas suppression' },
                { id: 3, name: 'Fire Alarm Systems', description: 'Control panels, sounders, strobes' },
                { id: 4, name: 'Emergency Lighting', description: 'Exit signs, emergency lights, central battery systems' },
                { id: 5, name: 'Fire Doors & Hardware', description: 'Fire doors, door closers, panic hardware' },
                { id: 6, name: 'Passive Fire Protection', description: 'Fire stopping, fire barriers, fire dampers' },
                { id: 7, name: 'Installation & Labor', description: 'Installation costs, testing, commissioning' }
            ],
            settings: {
                currency: 'USD',
                taxRate: 0.1,
                profitMargin: 0.15,
                lastBackup: null
            }
        };
        
        this.loadData();
    }
    
    loadData() {
        try {
            if (fs.existsSync(this.dataFile)) {
                const data = fs.readFileSync(this.dataFile, 'utf8');
                this.data = JSON.parse(data);
                
                // Merge with default data to ensure all properties exist
                this.data = { ...this.defaultData, ...this.data };
                
                // Ensure arrays exist
                if (!this.data.projects) this.data.projects = [];
                if (!this.data.items) this.data.items = [];
                if (!this.data.categories) this.data.categories = this.defaultData.categories;
                if (!this.data.settings) this.data.settings = this.defaultData.settings;
                
            } else {
                this.data = { ...this.defaultData };
                this.saveData();
            }
        } catch (error) {
            console.error('Error loading data:', error);
            this.data = { ...this.defaultData };
            this.saveData();
        }
    }
    
    saveData() {
        try {
            // Create backup before saving
            if (fs.existsSync(this.dataFile)) {
                fs.copyFileSync(this.dataFile, this.backupFile);
            }
            
            // Save data
            fs.writeFileSync(this.dataFile, JSON.stringify(this.data, null, 2));
            this.data.settings.lastBackup = new Date().toISOString();
            
            return true;
        } catch (error) {
            console.error('Error saving data:', error);
            return false;
        }
    }
    
    // Project methods
    getProjects() {
        return this.data.projects || [];
    }
    
    getProject(id) {
        return this.data.projects.find(p => p.id === id);
    }
    
    addProject(project) {
        const newProject = {
            id: Date.now(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            ...project
        };
        this.data.projects.push(newProject);
        this.saveData();
        return newProject;
    }
    
    updateProject(id, updates) {
        const index = this.data.projects.findIndex(p => p.id === id);
        if (index !== -1) {
            this.data.projects[index] = {
                ...this.data.projects[index],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            this.saveData();
            return this.data.projects[index];
        }
        return null;
    }
    
    deleteProject(id) {
        const index = this.data.projects.findIndex(p => p.id === id);
        if (index !== -1) {
            const deleted = this.data.projects.splice(index, 1)[0];
            this.saveData();
            return deleted;
        }
        return null;
    }
    
    // Item methods
    getItems() {
        return this.data.items || [];
    }
    
    getItem(id) {
        return this.data.items.find(i => i.id === id);
    }
    
    addItem(item) {
        const newItem = {
            id: Date.now(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            ...item
        };
        this.data.items.push(newItem);
        this.saveData();
        return newItem;
    }
    
    updateItem(id, updates) {
        const index = this.data.items.findIndex(i => i.id === id);
        if (index !== -1) {
            this.data.items[index] = {
                ...this.data.items[index],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            this.saveData();
            return this.data.items[index];
        }
        return null;
    }
    
    deleteItem(id) {
        const index = this.data.items.findIndex(i => i.id === id);
        if (index !== -1) {
            const deleted = this.data.items.splice(index, 1)[0];
            this.saveData();
            return deleted;
        }
        return null;
    }
    
    // Category methods
    getCategories() {
        return this.data.categories || [];
    }
    
    // Settings methods
    getSettings() {
        return this.data.settings || {};
    }
    
    updateSettings(updates) {
        this.data.settings = { ...this.data.settings, ...updates };
        this.saveData();
        return this.data.settings;
    }
    
    // Utility methods
    exportData() {
        return JSON.stringify(this.data, null, 2);
    }
    
    importData(jsonData) {
        try {
            const importedData = JSON.parse(jsonData);
            this.data = { ...this.defaultData, ...importedData };
            this.saveData();
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }
    
    getDataPath() {
        return this.dataFile;
    }
    
    getBackupPath() {
        return this.backupFile;
    }
}

module.exports = DataManager;
