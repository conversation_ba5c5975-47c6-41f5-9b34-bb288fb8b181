/* AG Grid Custom Styles for FireTool */

.data-grid {
  flex: 1;
  margin: 20px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

/* AG Grid Theme Customization */
.ag-theme-alpine {
  --ag-header-background-color: var(--primary-color);
  --ag-header-foreground-color: white;
  --ag-header-cell-hover-background-color: var(--primary-dark);
  --ag-header-cell-moving-background-color: var(--primary-dark);
  
  --ag-row-hover-color: rgba(231, 76, 60, 0.1);
  --ag-selected-row-background-color: rgba(231, 76, 60, 0.2);
  
  --ag-border-color: var(--border-color);
  --ag-row-border-color: var(--border-color);
  
  --ag-font-family: var(--font-family);
  --ag-font-size: var(--font-size-base);
  
  --ag-cell-horizontal-padding: 12px;
  --ag-cell-vertical-padding: 8px;
  
  --ag-grid-size: 4px;
  --ag-icon-size: 16px;
}

/* Header Styles */
.ag-header {
  border-bottom: 2px solid var(--primary-dark);
}

.ag-header-cell {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: var(--font-size-sm);
}

.ag-header-cell-label {
  justify-content: center;
}

/* Cell Styles */
.ag-cell {
  display: flex;
  align-items: center;
  border-right: 1px solid var(--border-color);
}

.ag-cell:last-child {
  border-right: none;
}

/* Row Styles */
.ag-row {
  border-bottom: 1px solid var(--border-color);
}

.ag-row:hover {
  background-color: var(--row-hover-color) !important;
}

.ag-row-selected {
  background-color: var(--selected-row-background-color) !important;
}

/* Custom Cell Renderers */
.cell-price {
  font-weight: bold;
  color: var(--primary-color);
  text-align: right;
}

.cell-currency::before {
  content: '$';
  margin-right: 2px;
}

.cell-status {
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-align: center;
}

.cell-status.active {
  background-color: var(--success-color);
  color: white;
}

.cell-status.inactive {
  background-color: var(--text-muted);
  color: white;
}

.cell-status.draft {
  background-color: var(--warning-color);
  color: white;
}

.cell-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.cell-action-btn {
  padding: 4px 8px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: var(--transition);
}

.cell-action-btn.edit {
  background-color: var(--secondary-color);
  color: white;
}

.cell-action-btn.delete {
  background-color: var(--danger-color);
  color: white;
}

.cell-action-btn:hover {
  transform: scale(1.1);
}

/* Pagination */
.ag-paging-panel {
  border-top: 1px solid var(--border-color);
  background-color: var(--light-color);
  padding: 10px;
}

.ag-paging-button {
  background-color: white;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  margin: 0 2px;
  border-radius: var(--border-radius);
}

.ag-paging-button:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.ag-paging-button.ag-disabled {
  background-color: var(--light-color);
  color: var(--text-muted);
  cursor: not-allowed;
}

/* Filtering */
.ag-header-cell-menu-button {
  color: white;
}

.ag-header-cell-menu-button:hover {
  color: var(--light-color);
}

.ag-filter-toolpanel {
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.ag-filter-toolpanel-header {
  background-color: var(--light-color);
  border-bottom: 1px solid var(--border-color);
  padding: 10px;
  font-weight: 600;
}

/* Context Menu */
.ag-menu {
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.ag-menu-option {
  padding: 8px 12px;
  cursor: pointer;
  transition: var(--transition);
}

.ag-menu-option:hover {
  background-color: var(--light-color);
}

.ag-menu-separator {
  border-top: 1px solid var(--border-color);
  margin: 4px 0;
}

/* Loading Overlay */
.ag-overlay-loading-wrapper {
  background-color: rgba(255, 255, 255, 0.9);
}

.ag-overlay-loading-center {
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--box-shadow);
}

/* No Rows Overlay */
.ag-overlay-no-rows-wrapper {
  background-color: rgba(255, 255, 255, 0.9);
}

.ag-overlay-no-rows-center {
  color: var(--text-muted);
  font-size: var(--font-size-lg);
}

/* Column Resizing */
.ag-header-cell-resize {
  background-color: var(--primary-color);
  width: 2px;
}

/* Selection Checkbox */
.ag-selection-checkbox {
  color: var(--primary-color);
}

.ag-checkbox-input-wrapper {
  border-color: var(--primary-color);
}

.ag-checkbox-input-wrapper.ag-checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Sorting */
.ag-header-cell-sorted-asc .ag-header-cell-label::after {
  content: ' ↑';
  color: white;
}

.ag-header-cell-sorted-desc .ag-header-cell-label::after {
  content: ' ↓';
  color: white;
}

/* Custom Grid Toolbar */
.grid-toolbar {
  background-color: var(--light-color);
  border-bottom: 1px solid var(--border-color);
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.grid-toolbar-left {
  display: flex;
  gap: 10px;
  align-items: center;
}

.grid-toolbar-right {
  display: flex;
  gap: 10px;
  align-items: center;
}

.grid-search {
  position: relative;
}

.grid-search input {
  padding: 6px 12px 6px 30px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  width: 250px;
}

.grid-search .icon-search {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.grid-info {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* Responsive Grid */
@media (max-width: 768px) {
  .data-grid {
    margin: 10px;
  }
  
  .ag-cell {
    font-size: var(--font-size-sm);
    padding: 6px;
  }
  
  .ag-header-cell {
    font-size: var(--font-size-sm);
    padding: 6px;
  }
  
  .grid-toolbar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .grid-search input {
    width: 100%;
  }
}

/* Print Styles */
@media print {
  .ag-theme-alpine {
    --ag-header-background-color: #f5f5f5;
    --ag-header-foreground-color: black;
  }
  
  .ag-header-cell,
  .ag-cell {
    border: 1px solid #ccc !important;
  }
  
  .grid-toolbar {
    display: none;
  }
}
