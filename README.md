# FireTool - Cost Estimation and Offer Generation Platform

FireTool is a comprehensive desktop application built with Electron.js for cost estimation and offer generation in the fire safety industry. It provides powerful tools for managing product catalogs, creating Bills of Quantities (BOQs), and generating professional estimates.

## Features

### 🔥 Core Functionality
- **Product Catalog Management**: Organize fire safety equipment across multiple categories
- **BOQ Builder**: Drag-and-drop interface for creating detailed Bills of Quantities
- **Cost Estimation**: Generate accurate cost estimates with markup and discount calculations
- **Excel Integration**: Import/export data from Excel files with automatic table creation
- **Professional Reports**: Generate PDF reports and quotations

### 🗄️ Database Management
- **Local SQLite Database**: High-performance offline-first storage
- **Excel Import**: Convert Excel sheets to structured database tables
- **Data Validation**: Automatic data type detection and validation
- **Backup & Restore**: Automated local database backups

### 🌐 Cloud Integration
- **Supabase Sync**: Background synchronization with cloud database
- **Team Collaboration**: Share pricing data and updates across team members
- **Conflict Resolution**: Handle concurrent data modifications
- **Offline-First**: Full functionality without internet connection

### 🎨 User Interface
- **Native Desktop Feel**: Built with Electron for native performance
- **Excel-like Grid**: Familiar spreadsheet interface with advanced features
- **Drag & Drop**: Intuitive file import and BOQ building
- **Keyboard Shortcuts**: Full desktop keyboard shortcut support
- **Multi-Window**: Compare data across multiple windows

## Fire Safety Categories

FireTool supports comprehensive product management across all major fire safety categories:

- **Fire Alarm Systems**: Detection and notification equipment
- **Firefighting Systems**: Active fire suppression equipment
- **Clean Agent Systems**: Environmentally friendly suppression
- **Foam Systems**: Foam-based fire suppression
- **CO2 Systems**: Carbon dioxide suppression systems
- **Fire Pumps**: Fire water pumps and related equipment
- **Foam Pumps**: Foam system pumps and equipment
- **Civil Works**: Construction and installation services

## Installation

### Prerequisites
- Node.js 16 or higher
- npm or yarn package manager

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd firetool
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

### Production Build

1. **Build for current platform**
   ```bash
   npm run build
   ```

2. **Build for specific platforms**
   ```bash
   npm run build-win    # Windows
   npm run build-mac    # macOS
   npm run build-linux  # Linux
   ```

## Usage

### Getting Started

1. **Launch FireTool** and create a new project
2. **Import your data** using Excel files or manual entry
3. **Organize products** into appropriate fire safety categories
4. **Build BOQs** using the drag-and-drop interface
5. **Generate estimates** with professional formatting

### Excel Import

FireTool can automatically import Excel files and convert them to structured database tables:

- Supports `.xlsx`, `.xls`, and `.csv` formats
- Automatic data type detection
- Smart column mapping
- Handles multiple worksheets
- Preserves data relationships

### BOQ Creation

The BOQ Builder provides an intuitive interface for creating detailed Bills of Quantities:

1. Browse the product catalog by category
2. Drag products into your BOQ or double-click to add
3. Edit quantities, prices, and descriptions inline
4. Apply markups and discounts as needed
5. Export to Excel or PDF formats

### Cost Estimation

Generate professional cost estimates with:

- Automatic quantity calculations
- Multi-currency support
- Tax calculations
- Regional pricing adjustments
- Professional PDF reports
- Historical pricing data

## Database Schema

FireTool uses a comprehensive SQLite database with the following main tables:

- **products**: Product catalog with pricing and specifications
- **categories**: Hierarchical product categorization
- **suppliers**: Vendor information and contact details
- **boq**: Bill of Quantities headers
- **boq_items**: Individual BOQ line items
- **estimates**: Cost estimates and quotations
- **projects**: Project management and organization
- **labor_rates**: Labor cost information by region and skill level

## Configuration

### Supabase Integration

To enable cloud synchronization:

1. Create a Supabase project
2. Set up the database schema (SQL scripts provided)
3. Configure environment variables:
   ```
   SUPABASE_URL=your-project-url
   SUPABASE_ANON_KEY=your-anon-key
   ```

### Application Settings

Settings are stored locally and include:

- Default currency and regional settings
- Grid display preferences
- Auto-save intervals
- Backup schedules
- Sync preferences

## File Formats

### Project Files (.firetool)
FireTool projects are saved as JSON files containing:
- Project metadata
- BOQ data
- Estimates
- Settings and preferences

### Excel Import/Export
Supported formats:
- `.xlsx` (Excel 2007+)
- `.xls` (Excel 97-2003)
- `.csv` (Comma-separated values)

## Keyboard Shortcuts

- `Ctrl+N` / `Cmd+N`: New project/BOQ
- `Ctrl+O` / `Cmd+O`: Open project
- `Ctrl+S` / `Cmd+S`: Save current work
- `Ctrl+I` / `Cmd+I`: Import Excel file
- `Ctrl+E` / `Cmd+E`: Export to Excel
- `Ctrl+F` / `Cmd+F`: Focus search
- `F5`: Refresh current view
- `Escape`: Close modals/cancel operations

## Development

### Project Structure
```
src/
├── main.js                 # Electron main process
├── database/
│   └── DatabaseManager.js  # SQLite database management
├── services/
│   ├── ExcelImporter.js    # Excel import functionality
│   ├── ProjectManager.js   # Project file management
│   └── SupabaseSync.js     # Cloud synchronization
└── renderer/
    ├── index.html          # Main application UI
    ├── js/
    │   ├── app.js          # Main application logic
    │   ├── database.js     # Database interface
    │   ├── grid.js         # AG Grid components
    │   ├── boq.js          # BOQ management
    │   ├── ui.js           # UI management
    │   └── utils.js        # Utility functions
    └── styles/
        ├── main.css        # Main styles
        ├── components.css  # Component styles
        └── grid.css        # Grid-specific styles
```

### Technologies Used

- **Electron**: Desktop application framework
- **Node.js**: Backend runtime
- **SQLite/better-sqlite3**: Local database
- **AG Grid**: Advanced data grid component
- **ExcelJS**: Excel file processing
- **Chart.js**: Data visualization
- **Supabase**: Cloud database and sync

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Contact the development team

## Roadmap

### Upcoming Features
- Advanced reporting templates
- Mobile companion app
- Integration with accounting systems
- Multi-language support
- Advanced pricing rules engine
- Project templates and wizards

### Version History
- **v1.0.0**: Initial release with core functionality
- **v1.1.0**: Enhanced Excel import and BOQ features (planned)
- **v1.2.0**: Advanced reporting and templates (planned)
