const ExcelJS = require('exceljs');
const csv = require('csv-parser');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class ExcelImporter {
  constructor(databaseManager) {
    this.db = databaseManager;
  }

  async importFile(filePath, options = {}) {
    const fileExtension = path.extname(filePath).toLowerCase();
    
    try {
      let result;
      
      if (fileExtension === '.csv') {
        result = await this.importCSV(filePath, options);
      } else if (fileExtension === '.xlsx' || fileExtension === '.xls') {
        result = await this.importExcel(filePath, options);
      } else {
        throw new Error('Unsupported file format. Please use Excel (.xlsx, .xls) or CSV files.');
      }

      // Log import history
      this.logImportHistory(filePath, result);
      
      return result;
    } catch (error) {
      console.error('Import failed:', error);
      this.logImportHistory(filePath, { error: error.message, recordsImported: 0, recordsFailed: 0 });
      throw error;
    }
  }

  async importExcel(filePath, options = {}) {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);
    
    const results = {
      sheets: [],
      totalRecordsImported: 0,
      totalRecordsFailed: 0,
      errors: []
    };

    // Process each worksheet
    workbook.eachSheet((worksheet, sheetId) => {
      try {
        const sheetResult = this.processWorksheet(worksheet, options);
        results.sheets.push({
          name: worksheet.name,
          ...sheetResult
        });
        results.totalRecordsImported += sheetResult.recordsImported;
        results.totalRecordsFailed += sheetResult.recordsFailed;
      } catch (error) {
        results.errors.push(`Sheet ${worksheet.name}: ${error.message}`);
        results.totalRecordsFailed++;
      }
    });

    return results;
  }

  processWorksheet(worksheet, options = {}) {
    const data = [];
    const errors = [];
    let headers = [];
    let recordsImported = 0;
    let recordsFailed = 0;

    // Get headers from first row
    const firstRow = worksheet.getRow(1);
    firstRow.eachCell((cell, colNumber) => {
      headers[colNumber] = this.cleanHeaderName(cell.value);
    });

    // Process data rows
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);
      
      // Skip empty rows
      if (this.isEmptyRow(row)) continue;

      try {
        const rowData = this.processRow(row, headers);
        
        if (this.validateRowData(rowData)) {
          data.push(rowData);
          recordsImported++;
        } else {
          errors.push(`Row ${rowNumber}: Invalid data`);
          recordsFailed++;
        }
      } catch (error) {
        errors.push(`Row ${rowNumber}: ${error.message}`);
        recordsFailed++;
      }
    }

    // Determine target table and import data
    const tableName = this.determineTargetTable(worksheet.name, headers);
    
    if (tableName && data.length > 0) {
      this.importToTable(tableName, data, headers);
    }

    return {
      tableName,
      headers,
      data,
      recordsImported,
      recordsFailed,
      errors
    };
  }

  processRow(row, headers) {
    const rowData = { id: uuidv4() };
    
    row.eachCell((cell, colNumber) => {
      const header = headers[colNumber];
      if (header) {
        rowData[header] = this.processCellValue(cell);
      }
    });

    return rowData;
  }

  processCellValue(cell) {
    if (!cell || cell.value === null || cell.value === undefined) {
      return null;
    }

    // Handle different cell types
    switch (cell.type) {
      case ExcelJS.ValueType.Number:
        return cell.value;
      
      case ExcelJS.ValueType.String:
        return cell.value.trim();
      
      case ExcelJS.ValueType.Date:
        return cell.value.toISOString();
      
      case ExcelJS.ValueType.Boolean:
        return cell.value;
      
      case ExcelJS.ValueType.Formula:
        return cell.result || cell.value;
      
      default:
        return String(cell.value).trim();
    }
  }

  cleanHeaderName(headerValue) {
    if (!headerValue) return '';
    
    return String(headerValue)
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
  }

  isEmptyRow(row) {
    let hasData = false;
    row.eachCell((cell) => {
      if (cell.value !== null && cell.value !== undefined && cell.value !== '') {
        hasData = true;
      }
    });
    return !hasData;
  }

  validateRowData(rowData) {
    // Basic validation - ensure at least one non-id field has data
    const dataFields = Object.keys(rowData).filter(key => key !== 'id');
    return dataFields.some(field => rowData[field] !== null && rowData[field] !== '');
  }

  determineTargetTable(sheetName, headers) {
    const sheetNameLower = sheetName.toLowerCase();
    
    // Map sheet names to database tables
    const tableMapping = {
      'products': 'products',
      'product': 'products',
      'items': 'products',
      'catalog': 'products',
      'suppliers': 'suppliers',
      'supplier': 'suppliers',
      'vendors': 'suppliers',
      'vendor': 'suppliers',
      'labor': 'labor_rates',
      'labor_rates': 'labor_rates',
      'labour': 'labor_rates',
      'categories': 'categories',
      'category': 'categories'
    };

    // Check for exact matches first
    if (tableMapping[sheetNameLower]) {
      return tableMapping[sheetNameLower];
    }

    // Check for partial matches
    for (const [key, table] of Object.entries(tableMapping)) {
      if (sheetNameLower.includes(key)) {
        return table;
      }
    }

    // Default to products if it looks like product data
    if (headers.includes('name') || headers.includes('product_name') || 
        headers.includes('description') || headers.includes('price')) {
      return 'products';
    }

    return 'products'; // Default fallback
  }

  importToTable(tableName, data, headers) {
    const transaction = this.db.db.transaction(() => {
      for (const row of data) {
        try {
          // Map headers to actual table columns
          const mappedData = this.mapToTableColumns(tableName, row);
          this.db.insert(tableName, mappedData);
        } catch (error) {
          console.error(`Failed to insert row into ${tableName}:`, error);
          throw error;
        }
      }
    });

    transaction();
  }

  mapToTableColumns(tableName, rowData) {
    const columnMappings = {
      products: {
        'name': 'name',
        'product_name': 'name',
        'description': 'description',
        'price': 'base_price',
        'unit_price': 'base_price',
        'base_price': 'base_price',
        'unit': 'unit',
        'code': 'code',
        'product_code': 'code',
        'sku': 'code',
        'category': 'category_id',
        'supplier': 'supplier_id'
      },
      suppliers: {
        'name': 'name',
        'supplier_name': 'name',
        'contact': 'contact_person',
        'contact_person': 'contact_person',
        'email': 'email',
        'phone': 'phone',
        'address': 'address'
      },
      labor_rates: {
        'category': 'category',
        'skill_level': 'skill_level',
        'rate': 'hourly_rate',
        'hourly_rate': 'hourly_rate',
        'region': 'region'
      }
    };

    const mapping = columnMappings[tableName] || {};
    const mappedData = {};

    // Map known columns
    for (const [sourceCol, targetCol] of Object.entries(mapping)) {
      if (rowData[sourceCol] !== undefined) {
        mappedData[targetCol] = rowData[sourceCol];
      }
    }

    // Add ID if not present
    if (!mappedData.id) {
      mappedData.id = rowData.id || uuidv4();
    }

    // Add default values for required fields
    if (tableName === 'products') {
      if (!mappedData.category_id) {
        // Get default category
        const defaultCategory = this.db.findAll('categories', { name: 'Fire Alarm Systems' })[0];
        mappedData.category_id = defaultCategory ? defaultCategory.id : uuidv4();
      }
    }

    return mappedData;
  }

  async importCSV(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      const data = [];
      const errors = [];
      let recordsImported = 0;
      let recordsFailed = 0;

      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => {
          try {
            const processedRow = { id: uuidv4(), ...row };
            if (this.validateRowData(processedRow)) {
              data.push(processedRow);
              recordsImported++;
            } else {
              recordsFailed++;
            }
          } catch (error) {
            errors.push(error.message);
            recordsFailed++;
          }
        })
        .on('end', () => {
          try {
            // Import to products table by default for CSV
            if (data.length > 0) {
              this.importToTable('products', data, Object.keys(data[0]));
            }
            
            resolve({
              recordsImported,
              recordsFailed,
              errors,
              data
            });
          } catch (error) {
            reject(error);
          }
        })
        .on('error', reject);
    });
  }

  logImportHistory(filePath, result) {
    const importRecord = {
      id: uuidv4(),
      filename: path.basename(filePath),
      file_path: filePath,
      import_type: path.extname(filePath),
      records_imported: result.totalRecordsImported || result.recordsImported || 0,
      records_failed: result.totalRecordsFailed || result.recordsFailed || 0,
      status: result.error ? 'failed' : 'completed',
      error_log: result.error || (result.errors && result.errors.join('\n')) || null
    };

    this.db.insert('import_history', importRecord);
  }
}

module.exports = ExcelImporter;
