const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class ExcelImporter {
  constructor(databaseManager) {
    this.db = databaseManager;
  }

  async importFile(filePath, options = {}) {
    const fileExtension = path.extname(filePath).toLowerCase();

    try {
      let result;

      if (fileExtension === '.csv') {
        result = await this.importCSV(filePath, options);
      } else if (fileExtension === '.xlsx' || fileExtension === '.xls') {
        result = await this.importExcel(filePath, options);
      } else {
        throw new Error('Unsupported file format. Please use Excel (.xlsx, .xls) or CSV files.');
      }

      // Log import history
      this.logImportHistory(filePath, result);

      return result;
    } catch (error) {
      console.error('Import failed:', error);
      this.logImportHistory(filePath, { error: error.message, recordsImported: 0, recordsFailed: 0 });
      throw error;
    }
  }

  async importExcel(filePath, options = {}) {
    const workbook = XLSX.readFile(filePath);

    const results = {
      sheets: [],
      totalRecordsImported: 0,
      totalRecordsFailed: 0,
      errors: []
    };

    // Process each worksheet
    workbook.SheetNames.forEach(sheetName => {
      try {
        const worksheet = workbook.Sheets[sheetName];
        const sheetResult = this.processWorksheet(worksheet, sheetName, options);
        results.sheets.push({
          name: sheetName,
          ...sheetResult
        });
        results.totalRecordsImported += sheetResult.recordsImported;
        results.totalRecordsFailed += sheetResult.recordsFailed;
      } catch (error) {
        results.errors.push(`Sheet ${sheetName}: ${error.message}`);
        results.totalRecordsFailed++;
      }
    });

    return results;
  }

  processWorksheet(worksheet, sheetName, options = {}) {
    const data = [];
    const errors = [];
    let recordsImported = 0;
    let recordsFailed = 0;

    // Convert worksheet to JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    if (jsonData.length === 0) {
      return {
        tableName: null,
        headers: [],
        data: [],
        recordsImported: 0,
        recordsFailed: 0,
        errors: ['Empty worksheet']
      };
    }

    // Get headers from first row
    const headers = jsonData[0].map(header => this.cleanHeaderName(header));

    // Process data rows
    for (let rowIndex = 1; rowIndex < jsonData.length; rowIndex++) {
      const row = jsonData[rowIndex];

      // Skip empty rows
      if (!row || row.every(cell => !cell)) continue;

      try {
        const rowData = this.processRow(row, headers);

        if (this.validateRowData(rowData)) {
          data.push(rowData);
          recordsImported++;
        } else {
          errors.push(`Row ${rowIndex + 1}: Invalid data`);
          recordsFailed++;
        }
      } catch (error) {
        errors.push(`Row ${rowIndex + 1}: ${error.message}`);
        recordsFailed++;
      }
    }

    // Determine target table and import data
    const tableName = this.determineTargetTable(sheetName, headers);

    if (tableName && data.length > 0) {
      this.importToTable(tableName, data, headers);
    }

    return {
      tableName,
      headers,
      data,
      recordsImported,
      recordsFailed,
      errors
    };
  }

  processRow(row, headers) {
    const rowData = { uuid: uuidv4() };

    headers.forEach((header, index) => {
      if (header && row[index] !== undefined) {
        rowData[header] = this.processCellValue(row[index]);
      }
    });

    return rowData;
  }

  processCellValue(cellValue) {
    if (cellValue === null || cellValue === undefined || cellValue === '') {
      return null;
    }

    // Handle different value types
    if (typeof cellValue === 'number') {
      return cellValue;
    }

    if (typeof cellValue === 'boolean') {
      return cellValue;
    }

    if (typeof cellValue === 'string') {
      return cellValue.trim();
    }

    // Handle dates (Excel dates come as numbers)
    if (typeof cellValue === 'number' && cellValue > 25000 && cellValue < 50000) {
      // Likely an Excel date
      const date = XLSX.SSF.parse_date_code(cellValue);
      return new Date(date.y, date.m - 1, date.d).toISOString();
    }

    return String(cellValue).trim();
  }

  cleanHeaderName(headerValue) {
    if (!headerValue) return '';

    return String(headerValue)
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
  }



  validateRowData(rowData) {
    // Basic validation - ensure at least one non-uuid field has data
    const dataFields = Object.keys(rowData).filter(key => key !== 'uuid' && key !== 'id');
    return dataFields.some(field => rowData[field] !== null && rowData[field] !== '');
  }

  determineTargetTable(sheetName, headers) {
    const sheetNameLower = sheetName.toLowerCase();

    // Map sheet names to database tables
    const tableMapping = {
      'products': 'products',
      'product': 'products',
      'items': 'products',
      'catalog': 'products',
      'suppliers': 'suppliers',
      'supplier': 'suppliers',
      'vendors': 'suppliers',
      'vendor': 'suppliers',
      'labor': 'labor_rates',
      'labor_rates': 'labor_rates',
      'labour': 'labor_rates',
      'categories': 'categories',
      'category': 'categories'
    };

    // Check for exact matches first
    if (tableMapping[sheetNameLower]) {
      return tableMapping[sheetNameLower];
    }

    // Check for partial matches
    for (const [key, table] of Object.entries(tableMapping)) {
      if (sheetNameLower.includes(key)) {
        return table;
      }
    }

    // Default to products if it looks like product data
    if (headers.includes('name') || headers.includes('product_name') ||
        headers.includes('description') || headers.includes('price')) {
      return 'products';
    }

    return 'products'; // Default fallback
  }

  importToTable(tableName, data, headers) {
    const transaction = this.db.db.transaction(() => {
      for (const row of data) {
        try {
          // Map headers to actual table columns
          const mappedData = this.mapToTableColumns(tableName, row);
          this.db.insert(tableName, mappedData);
        } catch (error) {
          console.error(`Failed to insert row into ${tableName}:`, error);
          throw error;
        }
      }
    });

    transaction();
  }

  mapToTableColumns(tableName, rowData) {
    const columnMappings = {
      products: {
        'name': 'name',
        'product_name': 'name',
        'description': 'description',
        'price': 'base_price',
        'unit_price': 'base_price',
        'base_price': 'base_price',
        'unit': 'unit',
        'code': 'code',
        'product_code': 'code',
        'sku': 'code',
        'category': 'category_id',
        'supplier': 'supplier_id'
      },
      suppliers: {
        'name': 'name',
        'supplier_name': 'name',
        'contact': 'contact_person',
        'contact_person': 'contact_person',
        'email': 'email',
        'phone': 'phone',
        'address': 'address'
      },
      labor_rates: {
        'category': 'category',
        'skill_level': 'skill_level',
        'rate': 'hourly_rate',
        'hourly_rate': 'hourly_rate',
        'region': 'region'
      }
    };

    const mapping = columnMappings[tableName] || {};
    const mappedData = {};

    // Map known columns
    for (const [sourceCol, targetCol] of Object.entries(mapping)) {
      if (rowData[sourceCol] !== undefined) {
        mappedData[targetCol] = rowData[sourceCol];
      }
    }

    // Add UUID if not present
    if (!mappedData.uuid) {
      mappedData.uuid = rowData.uuid || uuidv4();
    }

    // Add default values for required fields
    if (tableName === 'products') {
      if (!mappedData.category_id) {
        // Get default category
        const defaultCategory = this.db.findAll('categories', { name: 'Fire Alarm Systems' })[0];
        mappedData.category_id = defaultCategory ? defaultCategory.id : uuidv4();
      }
    }

    return mappedData;
  }

  async importCSV(filePath, options = {}) {
    // Simple CSV import using XLSX
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    return this.processWorksheet(worksheet, 'CSV Import', options);
  }

  logImportHistory(filePath, result) {
    const importRecord = {
      uuid: uuidv4(),
      filename: path.basename(filePath),
      file_path: filePath,
      import_type: path.extname(filePath),
      records_imported: result.totalRecordsImported || result.recordsImported || 0,
      records_failed: result.totalRecordsFailed || result.recordsFailed || 0,
      status: result.error ? 'failed' : 'completed',
      error_log: result.error || (result.errors && result.errors.join('\n')) || null
    };

    // Only log if we have a proper database connection
    try {
      this.db.insert('import_history', importRecord);
    } catch (error) {
      console.warn('Could not log import history:', error.message);
    }
  }
}

module.exports = ExcelImporter;
