/* FireTool Main Styles */

:root {
  --primary-color: #e74c3c;
  --primary-dark: #c0392b;
  --secondary-color: #3498db;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --dark-color: #2c3e50;
  --light-color: #ecf0f1;
  --border-color: #bdc3c7;
  --text-color: #2c3e50;
  --text-muted: #7f8c8d;
  --background-color: #ffffff;
  --sidebar-bg: #34495e;
  --sidebar-text: #ecf0f1;
  --header-bg: #2c3e50;
  --header-text: #ffffff;
  
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-base: 14px;
  --font-size-sm: 12px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  
  --border-radius: 4px;
  --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  color: var(--text-color);
  background-color: var(--background-color);
  overflow: hidden;
}

#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.app-header {
  background-color: var(--header-bg);
  color: var(--header-text);
  padding: 0 20px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo {
  height: 32px;
  width: auto;
}

.header-left h1 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin: 0;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.project-info {
  background-color: rgba(255,255,255,0.1);
  padding: 8px 16px;
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.sync-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: var(--font-size-sm);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--danger-color);
}

.status-indicator.online {
  background-color: var(--success-color);
}

.status-indicator.syncing {
  background-color: var(--warning-color);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Main Container */
.main-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  overflow-y: auto;
  border-right: 1px solid var(--border-color);
}

.sidebar-nav {
  padding: 20px 0;
}

.nav-section {
  margin-bottom: 30px;
}

.nav-section h3 {
  padding: 0 20px 10px;
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--text-muted);
  border-bottom: 1px solid rgba(255,255,255,0.1);
  margin-bottom: 10px;
}

.nav-section ul {
  list-style: none;
}

.nav-section li {
  margin-bottom: 2px;
}

.nav-link {
  display: block;
  padding: 12px 20px;
  color: var(--sidebar-text);
  text-decoration: none;
  transition: var(--transition);
  border-left: 3px solid transparent;
}

.nav-link:hover {
  background-color: rgba(255,255,255,0.1);
  border-left-color: var(--primary-color);
}

.nav-link.active {
  background-color: var(--primary-color);
  border-left-color: var(--primary-dark);
}

/* Content Area */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.view {
  display: none;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}

.view.active {
  display: flex;
}

.view-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--light-color);
}

.view-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
}

.view-actions {
  display: flex;
  gap: 10px;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--box-shadow);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #2980b9;
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Toolbar */
.toolbar {
  padding: 15px 20px;
  background-color: var(--light-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box input {
  width: 100%;
  padding: 8px 12px 8px 35px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
}

.search-box .icon-search {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.filters {
  display: flex;
  gap: 10px;
}

.filters select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  background-color: white;
}

/* Form Elements */
input, select, textarea {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

/* Loading Indicator */
.loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  color: white;
  flex-direction: column;
  gap: 20px;
}

.loading-indicator.show {
  display: flex;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255,255,255,0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .sidebar {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }
  
  .view-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
}
