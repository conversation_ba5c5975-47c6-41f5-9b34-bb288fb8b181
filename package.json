{"name": "firetool", "version": "1.0.0", "description": "FireTool - Cost Estimation and Offer Generation Platform", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "test": "jest", "lint": "eslint src/"}, "keywords": ["firetool", "cost-estimation", "boq", "fire-safety", "electron", "desktop"], "author": "FireTool Team", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "eslint": "^8.50.0", "jest": "^29.7.0", "@types/node": "^20.8.0"}, "dependencies": {"better-sqlite3": "^9.1.1", "xlsx": "^0.18.5", "ag-grid-community": "^31.0.0", "chart.js": "^4.4.0", "electron-updater": "^6.1.4", "fs-extra": "^11.1.1", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4"}, "build": {"appId": "com.firetool.app", "productName": "FireTool", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}