* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    color: #333;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.logo {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.logo h2 {
    font-size: 1.5em;
    font-weight: 300;
}

.nav-menu {
    list-style: none;
    padding: 20px 0;
    flex: 1;
}

.nav-menu li {
    margin: 5px 0;
}

.nav-menu a {
    display: block;
    padding: 12px 20px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-menu a:hover,
.nav-menu a.active {
    background: rgba(255,255,255,0.1);
    border-left-color: #ffd700;
}

.sidebar-footer {
    padding: 20px;
    text-align: center;
    border-top: 1px solid rgba(255,255,255,0.1);
    opacity: 0.7;
    font-size: 0.9em;
}

/* Main Content */
.main-content {
    flex: 1;
    overflow-y: auto;
    background: white;
}

#main-content {
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Loading */
.loading {
    text-align: center;
    padding: 100px 20px;
    color: #666;
}

/* Dashboard */
.dashboard h1 {
    font-size: 2.5em;
    margin-bottom: 30px;
    color: #333;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-card h3 {
    font-size: 1em;
    margin-bottom: 10px;
    opacity: 0.9;
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-card p {
    font-size: 0.9em;
    opacity: 0.8;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 40px;
}

.quick-actions h2 {
    margin-bottom: 20px;
    color: #333;
}

.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #dee2e6;
}

.btn-secondary:hover {
    background: #e9ecef;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

/* View Headers */
.view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.view-header h1 {
    font-size: 2em;
    color: #333;
}

/* Projects */
.projects-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.project-card {
    background: white;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.project-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.project-card h3 {
    margin-bottom: 10px;
    color: #333;
}

.project-meta {
    display: flex;
    justify-content: space-between;
    margin: 15px 0;
    font-size: 0.9em;
    color: #666;
}

.project-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

/* Items */
.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.item-card {
    background: white;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.item-card h4 {
    margin-bottom: 10px;
    color: #333;
}

.item-price {
    font-size: 1.2em;
    font-weight: bold;
    color: #28a745;
    margin: 10px 0;
}

.item-category {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 15px;
}

.item-actions {
    display: flex;
    gap: 10px;
}

/* Forms */
.settings-form {
    max-width: 500px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

/* Data Info */
.data-info {
    margin-top: 40px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.data-info h3 {
    margin-bottom: 10px;
    color: #333;
}

/* Recent Projects */
.recent-projects h2 {
    margin-bottom: 20px;
    color: #333;
}

.recent-project {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 10px;
}

.recent-project h4 {
    color: #333;
}

.recent-project span {
    color: #666;
    font-size: 0.9em;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
    font-size: 1.1em;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: #28a745;
}

.notification-error {
    background: #dc3545;
}

.notification-info {
    background: #17a2b8;
}

/* Responsive */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
    }
    
    .nav-menu {
        display: flex;
        padding: 10px;
    }
    
    .nav-menu li {
        flex: 1;
        margin: 0 5px;
    }
    
    .nav-menu a {
        text-align: center;
        padding: 10px 5px;
        font-size: 0.9em;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .action-buttons {
        justify-content: center;
    }
}
