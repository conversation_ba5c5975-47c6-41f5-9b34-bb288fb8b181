@echo off
title FireTool - Cost Estimation Platform
color 0A

echo.
echo  ========================================
echo   🔥 FireTool - Starting Application 🔥
echo  ========================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo  ❌ ERROR: Node.js is not installed!
    echo.
    echo  Please follow these steps:
    echo  1. Go to: https://nodejs.org/
    echo  2. Download and install Node.js LTS
    echo  3. Restart your computer
    echo  4. Run this file again
    echo.
    pause
    exit /b 1
)

echo  ✅ Node.js is installed
node --version

:: Check if dependencies are installed
if not exist "node_modules" (
    echo.
    echo  📦 Installing FireTool dependencies...
    echo  This may take a few minutes on first run...
    echo.
    npm install
    
    if %errorlevel% neq 0 (
        echo.
        echo  ❌ Failed to install dependencies
        echo  Please check your internet connection and try again
        echo.
        pause
        exit /b 1
    )
)

echo.
echo  🚀 Starting FireTool...
echo  The application window will open shortly...
echo.
echo  💡 Tips:
echo     - Keep this window open while using FireTool
echo     - Press Ctrl+C here to stop the application
echo     - Close the FireTool window to exit
echo.

:: Start the application
npm run dev

echo.
echo  FireTool has been closed.
echo  Thank you for using FireTool! 🔥
echo.
pause
