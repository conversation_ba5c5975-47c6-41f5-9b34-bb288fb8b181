const path = require('path');
const fs = require('fs');
const { app } = require('electron');
const { v4: uuidv4 } = require('uuid');

class DatabaseManager {
  constructor() {
    this.data = {
      projects: [],
      categories: [],
      products: [],
      suppliers: [],
      boq: [],
      boq_items: [],
      estimates: []
    };
    this.dataPath = path.join(app.getPath('userData'), 'firetool-data.json');
    this.backupPath = path.join(app.getPath('userData'), 'backups');
  }

  async initialize() {
    try {
      // Ensure backup directory exists
      if (!fs.existsSync(this.backupPath)) {
        fs.mkdirSync(this.backupPath, { recursive: true });
      }

      // Load existing data or create new
      this.loadData();

      // Insert default categories if none exist
      if (this.data.categories.length === 0) {
        this.insertDefaultCategories();
      }

      console.log('Database initialized successfully');
      return Promise.resolve();
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  loadData() {
    try {
      if (fs.existsSync(this.dataPath)) {
        const fileContent = fs.readFileSync(this.dataPath, 'utf8');
        this.data = JSON.parse(fileContent);
        console.log('Data loaded from file');
      } else {
        console.log('No existing data file, starting fresh');
      }
    } catch (error) {
      console.error('Error loading data:', error);
      // Start with empty data if file is corrupted
      this.data = {
        projects: [],
        categories: [],
        products: [],
        suppliers: [],
        boq: [],
        boq_items: [],
        estimates: []
      };
    }
  }

  saveData() {
    try {
      fs.writeFileSync(this.dataPath, JSON.stringify(this.data, null, 2));
      console.log('Data saved to file');
    } catch (error) {
      console.error('Error saving data:', error);
    }
  }

  insertDefaultCategories() {
    const defaultCategories = [
      { id: uuidv4(), name: 'Fire Alarm Systems', description: 'Fire detection and alarm equipment', sort_order: 0, created_at: new Date().toISOString() },
      { id: uuidv4(), name: 'Firefighting Systems', description: 'Active firefighting equipment', sort_order: 1, created_at: new Date().toISOString() },
      { id: uuidv4(), name: 'Clean Agent Systems', description: 'Clean agent fire suppression systems', sort_order: 2, created_at: new Date().toISOString() },
      { id: uuidv4(), name: 'Foam Systems', description: 'Foam-based fire suppression systems', sort_order: 3, created_at: new Date().toISOString() },
      { id: uuidv4(), name: 'CO2 Systems', description: 'Carbon dioxide fire suppression systems', sort_order: 4, created_at: new Date().toISOString() },
      { id: uuidv4(), name: 'Fire Pumps', description: 'Fire water pumps and equipment', sort_order: 5, created_at: new Date().toISOString() },
      { id: uuidv4(), name: 'Foam Pumps', description: 'Foam system pumps and equipment', sort_order: 6, created_at: new Date().toISOString() },
      { id: uuidv4(), name: 'Civil Works', description: 'Construction and installation work', sort_order: 7, created_at: new Date().toISOString() }
    ];

    this.data.categories = defaultCategories;
    this.saveData();
    console.log('Default categories inserted');
  }

  // Basic CRUD operations
  insert(table, data) {
    try {
      const id = data.id || uuidv4();
      const record = {
        id,
        ...data,
        created_at: data.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      if (!this.data[table]) {
        this.data[table] = [];
      }

      this.data[table].push(record);
      this.saveData();

      return Promise.resolve({ id, record });
    } catch (error) {
      return Promise.reject(error);
    }
  }

  update(table, id, data) {
    try {
      if (!this.data[table]) {
        throw new Error(`Table ${table} does not exist`);
      }

      const index = this.data[table].findIndex(item => item.id === id);
      if (index === -1) {
        throw new Error(`Record with id ${id} not found`);
      }

      this.data[table][index] = {
        ...this.data[table][index],
        ...data,
        updated_at: new Date().toISOString()
      };

      this.saveData();
      return Promise.resolve({ changes: 1 });
    } catch (error) {
      return Promise.reject(error);
    }
  }

  delete(table, id) {
    try {
      if (!this.data[table]) {
        throw new Error(`Table ${table} does not exist`);
      }

      const index = this.data[table].findIndex(item => item.id === id);
      if (index === -1) {
        throw new Error(`Record with id ${id} not found`);
      }

      this.data[table].splice(index, 1);
      this.saveData();

      return Promise.resolve({ changes: 1 });
    } catch (error) {
      return Promise.reject(error);
    }
  }

  findById(table, id) {
    try {
      if (!this.data[table]) {
        return Promise.resolve(null);
      }

      const record = this.data[table].find(item => item.id === id);
      return Promise.resolve(record || null);
    } catch (error) {
      return Promise.reject(error);
    }
  }

  findAll(table, conditions = {}) {
    try {
      if (!this.data[table]) {
        return Promise.resolve([]);
      }

      let results = [...this.data[table]];

      // Apply conditions
      if (Object.keys(conditions).length > 0) {
        results = results.filter(item => {
          return Object.entries(conditions).every(([key, value]) => {
            return item[key] === value;
          });
        });
      }

      return Promise.resolve(results);
    } catch (error) {
      return Promise.reject(error);
    }
  }

  backup() {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFile = path.join(this.backupPath, `firetool-backup-${timestamp}.json`);

      fs.writeFileSync(backupFile, JSON.stringify(this.data, null, 2));
      return Promise.resolve(backupFile);
    } catch (error) {
      return Promise.reject(error);
    }
  }

  close() {
    // Save data one final time
    this.saveData();
    console.log('Database closed');
  }
}

module.exports = DatabaseManager;
