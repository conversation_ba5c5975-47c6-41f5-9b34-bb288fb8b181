const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');
const { v4: uuidv4 } = require('uuid');

class DatabaseManager {
  constructor() {
    this.db = null;
    this.dbPath = path.join(app.getPath('userData'), 'firetool.db');
    this.backupPath = path.join(app.getPath('userData'), 'backups');
  }

  async initialize() {
    try {
      // Ensure backup directory exists
      if (!fs.existsSync(this.backupPath)) {
        fs.mkdirSync(this.backupPath, { recursive: true });
      }

      // Initialize database
      this.db = new Database(this.dbPath);
      this.db.pragma('journal_mode = WAL');
      this.db.pragma('synchronous = NORMAL');
      this.db.pragma('cache_size = 1000000');
      this.db.pragma('temp_store = memory');

      // Create tables
      await this.createTables();
      
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  createTables() {
    // Projects table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        client_name TEXT,
        project_location TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1,
        metadata TEXT
      )
    `);

    // Categories table for organizing products
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        parent_id TEXT,
        sort_order INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES categories(id)
      )
    `);

    // Products table - main product catalog
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        category_id TEXT NOT NULL,
        code TEXT UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        unit TEXT,
        base_price DECIMAL(15,2),
        currency TEXT DEFAULT 'USD',
        supplier_id TEXT,
        specifications TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1,
        FOREIGN KEY (category_id) REFERENCES categories(id),
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
      )
    `);

    // Suppliers table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS suppliers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        contact_person TEXT,
        email TEXT,
        phone TEXT,
        address TEXT,
        country TEXT,
        payment_terms TEXT,
        discount_percentage DECIMAL(5,2) DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1
      )
    `);

    // Labor rates table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS labor_rates (
        id TEXT PRIMARY KEY,
        category TEXT NOT NULL,
        skill_level TEXT NOT NULL,
        hourly_rate DECIMAL(10,2) NOT NULL,
        currency TEXT DEFAULT 'USD',
        region TEXT,
        effective_date DATE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1
      )
    `);

    // BOQ (Bill of Quantities) table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS boq (
        id TEXT PRIMARY KEY,
        project_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        status TEXT DEFAULT 'draft',
        total_amount DECIMAL(15,2) DEFAULT 0,
        FOREIGN KEY (project_id) REFERENCES projects(id)
      )
    `);

    // BOQ items table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS boq_items (
        id TEXT PRIMARY KEY,
        boq_id TEXT NOT NULL,
        product_id TEXT,
        item_description TEXT NOT NULL,
        quantity DECIMAL(15,3) NOT NULL,
        unit TEXT,
        unit_price DECIMAL(15,2) NOT NULL,
        total_price DECIMAL(15,2) NOT NULL,
        markup_percentage DECIMAL(5,2) DEFAULT 0,
        discount_percentage DECIMAL(5,2) DEFAULT 0,
        sort_order INTEGER DEFAULT 0,
        notes TEXT,
        FOREIGN KEY (boq_id) REFERENCES boq(id),
        FOREIGN KEY (product_id) REFERENCES products(id)
      )
    `);

    // Estimates table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS estimates (
        id TEXT PRIMARY KEY,
        project_id TEXT NOT NULL,
        boq_id TEXT,
        estimate_number TEXT UNIQUE,
        title TEXT NOT NULL,
        client_name TEXT,
        valid_until DATE,
        subtotal DECIMAL(15,2) DEFAULT 0,
        tax_percentage DECIMAL(5,2) DEFAULT 0,
        tax_amount DECIMAL(15,2) DEFAULT 0,
        total_amount DECIMAL(15,2) DEFAULT 0,
        status TEXT DEFAULT 'draft',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id),
        FOREIGN KEY (boq_id) REFERENCES boq(id)
      )
    `);

    // Import history table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS import_history (
        id TEXT PRIMARY KEY,
        filename TEXT NOT NULL,
        file_path TEXT,
        import_type TEXT NOT NULL,
        records_imported INTEGER DEFAULT 0,
        records_failed INTEGER DEFAULT 0,
        status TEXT DEFAULT 'completed',
        error_log TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Settings table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT,
        description TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Sync log table for Supabase integration
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS sync_log (
        id TEXT PRIMARY KEY,
        table_name TEXT NOT NULL,
        operation TEXT NOT NULL,
        record_id TEXT NOT NULL,
        sync_status TEXT DEFAULT 'pending',
        error_message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced_at DATETIME
      )
    `);

    // Create indexes for better performance
    this.createIndexes();
    
    // Insert default categories
    this.insertDefaultCategories();
  }

  createIndexes() {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)',
      'CREATE INDEX IF NOT EXISTS idx_products_code ON products(code)',
      'CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)',
      'CREATE INDEX IF NOT EXISTS idx_boq_project ON boq(project_id)',
      'CREATE INDEX IF NOT EXISTS idx_boq_items_boq ON boq_items(boq_id)',
      'CREATE INDEX IF NOT EXISTS idx_boq_items_product ON boq_items(product_id)',
      'CREATE INDEX IF NOT EXISTS idx_estimates_project ON estimates(project_id)',
      'CREATE INDEX IF NOT EXISTS idx_sync_log_table ON sync_log(table_name, sync_status)'
    ];

    indexes.forEach(indexSql => {
      this.db.exec(indexSql);
    });
  }

  insertDefaultCategories() {
    const defaultCategories = [
      { id: uuidv4(), name: 'Fire Alarm Systems', description: 'Fire detection and alarm equipment' },
      { id: uuidv4(), name: 'Firefighting Systems', description: 'Active firefighting equipment' },
      { id: uuidv4(), name: 'Clean Agent Systems', description: 'Clean agent fire suppression systems' },
      { id: uuidv4(), name: 'Foam Systems', description: 'Foam-based fire suppression systems' },
      { id: uuidv4(), name: 'CO2 Systems', description: 'Carbon dioxide fire suppression systems' },
      { id: uuidv4(), name: 'Fire Pumps', description: 'Fire water pumps and equipment' },
      { id: uuidv4(), name: 'Foam Pumps', description: 'Foam system pumps and equipment' },
      { id: uuidv4(), name: 'Civil Works', description: 'Construction and installation work' }
    ];

    const insertCategory = this.db.prepare(`
      INSERT OR IGNORE INTO categories (id, name, description, sort_order)
      VALUES (?, ?, ?, ?)
    `);

    defaultCategories.forEach((category, index) => {
      insertCategory.run(category.id, category.name, category.description, index);
    });
  }

  // CRUD operations
  insert(table, data) {
    const id = data.id || uuidv4();
    const columns = Object.keys(data);
    const placeholders = columns.map(() => '?').join(', ');
    const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;
    
    const stmt = this.db.prepare(sql);
    const result = stmt.run(...Object.values({ id, ...data }));
    
    return { id, ...result };
  }

  update(table, id, data) {
    const columns = Object.keys(data);
    const setClause = columns.map(col => `${col} = ?`).join(', ');
    const sql = `UPDATE ${table} SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
    
    const stmt = this.db.prepare(sql);
    return stmt.run(...Object.values(data), id);
  }

  delete(table, id) {
    const sql = `DELETE FROM ${table} WHERE id = ?`;
    const stmt = this.db.prepare(sql);
    return stmt.run(id);
  }

  findById(table, id) {
    const sql = `SELECT * FROM ${table} WHERE id = ?`;
    const stmt = this.db.prepare(sql);
    return stmt.get(id);
  }

  findAll(table, conditions = {}) {
    let sql = `SELECT * FROM ${table}`;
    const values = [];
    
    if (Object.keys(conditions).length > 0) {
      const whereClause = Object.keys(conditions)
        .map(key => `${key} = ?`)
        .join(' AND ');
      sql += ` WHERE ${whereClause}`;
      values.push(...Object.values(conditions));
    }
    
    const stmt = this.db.prepare(sql);
    return stmt.all(...values);
  }

  backup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(this.backupPath, `firetool-backup-${timestamp}.db`);
    
    this.db.backup(backupFile);
    return backupFile;
  }

  close() {
    if (this.db) {
      this.db.close();
    }
  }
}

module.exports = DatabaseManager;
