// AG Grid Components for FireTool

class BaseGrid {
  constructor(containerId) {
    this.containerId = containerId;
    this.gridApi = null;
    this.columnApi = null;
    this.gridOptions = null;
    this.data = [];
    
    this.init();
  }

  init() {
    this.gridOptions = {
      ...this.getDefaultOptions(),
      ...this.getCustomOptions()
    };

    const container = document.getElementById(this.containerId);
    if (container) {
      this.gridApi = agGrid.createGrid(container, this.gridOptions);
    }
  }

  getDefaultOptions() {
    return {
      defaultColDef: {
        sortable: true,
        filter: true,
        resizable: true,
        editable: false,
        cellStyle: { display: 'flex', alignItems: 'center' }
      },
      enableRangeSelection: true,
      enableClipboard: true,
      pagination: true,
      paginationPageSize: 50,
      rowSelection: 'multiple',
      suppressRowClickSelection: true,
      onGridReady: (params) => {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
        this.onGridReady(params);
      },
      onSelectionChanged: (params) => {
        this.onSelectionChanged(params);
      },
      onCellValueChanged: (params) => {
        this.onCellValueChanged(params);
      },
      getContextMenuItems: (params) => {
        return this.getContextMenuItems(params);
      }
    };
  }

  getCustomOptions() {
    return {};
  }

  onGridReady(params) {
    // Override in subclasses
  }

  onSelectionChanged(params) {
    // Override in subclasses
  }

  onCellValueChanged(params) {
    // Override in subclasses
  }

  getContextMenuItems(params) {
    return [
      'copy',
      'copyWithHeaders',
      'paste',
      'separator',
      {
        name: 'Edit',
        action: () => this.editRow(params.node.data),
        icon: '<span class="icon-edit"></span>'
      },
      {
        name: 'Delete',
        action: () => this.deleteRow(params.node.data),
        icon: '<span class="icon-delete"></span>'
      }
    ];
  }

  setData(data) {
    this.data = data;
    if (this.gridApi) {
      this.gridApi.setRowData(data);
    }
  }

  addRow(rowData) {
    this.data.push(rowData);
    if (this.gridApi) {
      this.gridApi.applyTransaction({ add: [rowData] });
    }
  }

  updateRow(rowData) {
    const index = this.data.findIndex(item => item.id === rowData.id);
    if (index !== -1) {
      this.data[index] = rowData;
      if (this.gridApi) {
        this.gridApi.applyTransaction({ update: [rowData] });
      }
    }
  }

  removeRow(id) {
    const index = this.data.findIndex(item => item.id === id);
    if (index !== -1) {
      const removedItem = this.data.splice(index, 1)[0];
      if (this.gridApi) {
        this.gridApi.applyTransaction({ remove: [removedItem] });
      }
    }
  }

  getSelectedRows() {
    return this.gridApi ? this.gridApi.getSelectedRows() : [];
  }

  exportToCsv() {
    if (this.gridApi) {
      this.gridApi.exportDataAsCsv();
    }
  }

  exportToExcel() {
    if (this.gridApi) {
      this.gridApi.exportDataAsExcel();
    }
  }

  editRow(rowData) {
    // Override in subclasses
    console.log('Edit row:', rowData);
  }

  deleteRow(rowData) {
    // Override in subclasses
    console.log('Delete row:', rowData);
  }

  destroy() {
    if (this.gridApi) {
      this.gridApi.destroy();
    }
  }
}

class ProductGrid extends BaseGrid {
  getCustomOptions() {
    return {
      columnDefs: [
        {
          headerName: '',
          checkboxSelection: true,
          headerCheckboxSelection: true,
          width: 50,
          pinned: 'left'
        },
        {
          headerName: 'Code',
          field: 'code',
          width: 120,
          pinned: 'left',
          cellStyle: { fontWeight: 'bold' }
        },
        {
          headerName: 'Name',
          field: 'name',
          width: 250,
          pinned: 'left'
        },
        {
          headerName: 'Description',
          field: 'description',
          width: 300,
          tooltipField: 'description'
        },
        {
          headerName: 'Category',
          field: 'category_name',
          width: 150,
          valueGetter: (params) => {
            return params.data.category_name || 'Uncategorized';
          }
        },
        {
          headerName: 'Unit',
          field: 'unit',
          width: 80,
          cellStyle: { textAlign: 'center' }
        },
        {
          headerName: 'Base Price',
          field: 'base_price',
          width: 120,
          cellClass: 'cell-price',
          cellRenderer: (params) => {
            if (params.value) {
              return `$${parseFloat(params.value).toFixed(2)}`;
            }
            return '';
          },
          cellStyle: { textAlign: 'right', fontWeight: 'bold' }
        },
        {
          headerName: 'Currency',
          field: 'currency',
          width: 80,
          cellStyle: { textAlign: 'center' }
        },
        {
          headerName: 'Supplier',
          field: 'supplier_name',
          width: 150
        },
        {
          headerName: 'Status',
          field: 'is_active',
          width: 100,
          cellRenderer: (params) => {
            const status = params.value ? 'Active' : 'Inactive';
            const className = params.value ? 'active' : 'inactive';
            return `<span class="cell-status ${className}">${status}</span>`;
          }
        },
        {
          headerName: 'Actions',
          width: 120,
          cellRenderer: (params) => {
            return `
              <div class="cell-actions">
                <button class="cell-action-btn edit" onclick="window.fireToolApp.grids.products.editRow('${params.data.id}')">
                  <span class="icon-edit"></span>
                </button>
                <button class="cell-action-btn delete" onclick="window.fireToolApp.grids.products.deleteRow('${params.data.id}')">
                  <span class="icon-delete"></span>
                </button>
              </div>
            `;
          },
          cellStyle: { textAlign: 'center' }
        }
      ]
    };
  }

  onGridReady(params) {
    // Auto-size columns
    params.api.sizeColumnsToFit();
  }

  editRow(id) {
    const rowData = this.data.find(item => item.id === id);
    if (rowData) {
      this.showEditModal(rowData);
    }
  }

  deleteRow(id) {
    if (confirm('Are you sure you want to delete this product?')) {
      // Call database delete
      window.fireToolApp.database.deleteProduct(id)
        .then(() => {
          this.removeRow(id);
          window.fireToolApp.showNotification('Success', 'Product deleted successfully', 'success');
        })
        .catch(error => {
          console.error('Failed to delete product:', error);
          window.fireToolApp.showNotification('Error', 'Failed to delete product', 'error');
        });
    }
  }

  showEditModal(productData) {
    const modalContent = `
      <div class="modal-header">
        <h3 class="modal-title">Edit Product</h3>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="edit-product-form">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">Code</label>
              <input type="text" class="form-control" name="code" value="${productData.code || ''}" required>
            </div>
            <div class="form-group">
              <label class="form-label">Name</label>
              <input type="text" class="form-control" name="name" value="${productData.name || ''}" required>
            </div>
          </div>
          <div class="form-group">
            <label class="form-label">Description</label>
            <textarea class="form-control" name="description" rows="3">${productData.description || ''}</textarea>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">Unit</label>
              <input type="text" class="form-control" name="unit" value="${productData.unit || ''}">
            </div>
            <div class="form-group">
              <label class="form-label">Base Price</label>
              <input type="number" class="form-control" name="base_price" value="${productData.base_price || ''}" step="0.01">
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">Currency</label>
              <select class="form-control" name="currency">
                <option value="USD" ${productData.currency === 'USD' ? 'selected' : ''}>USD</option>
                <option value="EUR" ${productData.currency === 'EUR' ? 'selected' : ''}>EUR</option>
                <option value="GBP" ${productData.currency === 'GBP' ? 'selected' : ''}>GBP</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">Category</label>
              <select class="form-control" name="category_id" id="edit-category-select">
                <!-- Categories will be populated -->
              </select>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" onclick="window.fireToolApp.hideModal()">Cancel</button>
        <button type="button" class="btn btn-primary" onclick="window.fireToolApp.grids.products.saveProduct('${productData.id}')">Save</button>
      </div>
    `;

    window.fireToolApp.showModal(modalContent);
    
    // Populate categories
    window.fireToolApp.database.getAllCategories().then(categories => {
      const select = document.getElementById('edit-category-select');
      categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        option.selected = category.id === productData.category_id;
        select.appendChild(option);
      });
    });
  }

  saveProduct(id) {
    const form = document.getElementById('edit-product-form');
    const formData = new FormData(form);
    const productData = Object.fromEntries(formData.entries());

    window.fireToolApp.database.updateProduct(id, productData)
      .then(() => {
        this.updateRow({ id, ...productData });
        window.fireToolApp.hideModal();
        window.fireToolApp.showNotification('Success', 'Product updated successfully', 'success');
      })
      .catch(error => {
        console.error('Failed to update product:', error);
        window.fireToolApp.showNotification('Error', 'Failed to update product', 'error');
      });
  }
}

class BOQGrid extends BaseGrid {
  getCustomOptions() {
    return {
      columnDefs: [
        {
          headerName: '',
          checkboxSelection: true,
          headerCheckboxSelection: true,
          width: 50
        },
        {
          headerName: 'Item',
          field: 'item_description',
          width: 300,
          editable: true
        },
        {
          headerName: 'Product Code',
          field: 'product_code',
          width: 120
        },
        {
          headerName: 'Unit',
          field: 'unit',
          width: 80,
          editable: true
        },
        {
          headerName: 'Quantity',
          field: 'quantity',
          width: 100,
          editable: true,
          cellEditor: 'agNumberCellEditor',
          cellStyle: { textAlign: 'right' }
        },
        {
          headerName: 'Unit Price',
          field: 'unit_price',
          width: 120,
          editable: true,
          cellEditor: 'agNumberCellEditor',
          cellRenderer: (params) => {
            if (params.value) {
              return `$${parseFloat(params.value).toFixed(2)}`;
            }
            return '';
          },
          cellStyle: { textAlign: 'right' }
        },
        {
          headerName: 'Total Price',
          field: 'total_price',
          width: 120,
          valueGetter: (params) => {
            const quantity = parseFloat(params.data.quantity) || 0;
            const unitPrice = parseFloat(params.data.unit_price) || 0;
            return quantity * unitPrice;
          },
          cellRenderer: (params) => {
            return `$${parseFloat(params.value).toFixed(2)}`;
          },
          cellStyle: { textAlign: 'right', fontWeight: 'bold' }
        },
        {
          headerName: 'Actions',
          width: 100,
          cellRenderer: (params) => {
            return `
              <div class="cell-actions">
                <button class="cell-action-btn delete" onclick="window.fireToolApp.grids.boq.deleteRow('${params.data.id}')">
                  <span class="icon-delete"></span>
                </button>
              </div>
            `;
          }
        }
      ]
    };
  }

  onCellValueChanged(params) {
    // Recalculate total when quantity or unit price changes
    if (params.colDef.field === 'quantity' || params.colDef.field === 'unit_price') {
      const quantity = parseFloat(params.data.quantity) || 0;
      const unitPrice = parseFloat(params.data.unit_price) || 0;
      params.data.total_price = quantity * unitPrice;
      
      // Refresh the row to update the total price display
      this.gridApi.refreshCells({ rowNodes: [params.node], force: true });
      
      // Update BOQ total
      this.updateBOQTotal();
    }
  }

  addBOQItem(itemData) {
    const newItem = {
      id: window.fireToolApp.database.generateId(),
      ...itemData,
      total_price: (itemData.quantity || 0) * (itemData.unit_price || 0)
    };
    
    this.addRow(newItem);
    this.updateBOQTotal();
  }

  updateBOQTotal() {
    const total = this.data.reduce((sum, item) => {
      return sum + ((item.quantity || 0) * (item.unit_price || 0));
    }, 0);
    
    const totalElement = document.getElementById('boq-total-amount');
    if (totalElement) {
      totalElement.textContent = total.toFixed(2);
    }
    
    const countElement = document.getElementById('boq-item-count');
    if (countElement) {
      countElement.textContent = this.data.length;
    }
  }

  deleteRow(id) {
    if (confirm('Are you sure you want to remove this item from the BOQ?')) {
      this.removeRow(id);
      this.updateBOQTotal();
    }
  }
}

// Export classes for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { BaseGrid, ProductGrid, BOQGrid };
}
