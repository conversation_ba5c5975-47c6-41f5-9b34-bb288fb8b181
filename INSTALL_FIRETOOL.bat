@echo off
title FireTool - Installation
color 0B

echo.
echo  ==========================================
echo   🔥 FireTool - Installation Wizard 🔥
echo  ==========================================
echo.
echo  This will set up FireTool on your computer.
echo.

:: Check if Node.js is installed
echo  Step 1: Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo  ❌ Node.js is NOT installed
    echo.
    echo  REQUIRED: Please install Node.js first
    echo.
    echo  1. Go to: https://nodejs.org/
    echo  2. Click the big green "LTS" button
    echo  3. Download and install
    echo  4. Restart your computer
    echo  5. Run this installer again
    echo.
    echo  Press any key to open Node.js website...
    pause >nul
    start https://nodejs.org/
    exit /b 1
) else (
    echo  ✅ Node.js is installed: 
    node --version
)

echo.
echo  Step 2: Installing FireTool dependencies...
echo  This will download required files (may take 2-5 minutes)
echo.

npm install

if %errorlevel% neq 0 (
    echo.
    echo  ❌ Installation failed!
    echo.
    echo  Common solutions:
    echo  - Check your internet connection
    echo  - Run as Administrator (right-click this file)
    echo  - Temporarily disable antivirus
    echo.
    pause
    exit /b 1
)

echo.
echo  ✅ Installation completed successfully!
echo.
echo  Step 3: Creating desktop shortcut...

:: Create a simple shortcut script
echo @echo off > "%USERPROFILE%\Desktop\Start FireTool.bat"
echo cd /d "%~dp0" >> "%USERPROFILE%\Desktop\Start FireTool.bat"
echo npm run dev >> "%USERPROFILE%\Desktop\Start FireTool.bat"

echo  ✅ Desktop shortcut created
echo.
echo  ==========================================
echo   🎉 FireTool Installation Complete! 🎉
echo  ==========================================
echo.
echo  How to start FireTool:
echo.
echo  Option 1: Double-click "RUN_FIRETOOL.bat"
echo  Option 2: Use the desktop shortcut
echo  Option 3: Open Command Prompt and type:
echo            cd "%~dp0"
echo            npm run dev
echo.
echo  📖 For help, read "START_HERE.md"
echo.
echo  Would you like to start FireTool now? (Y/N)
set /p choice="> "

if /i "%choice%"=="Y" (
    echo.
    echo  🚀 Starting FireTool...
    start "" "%~dp0RUN_FIRETOOL.bat"
) else (
    echo.
    echo  You can start FireTool anytime using "RUN_FIRETOOL.bat"
)

echo.
echo  Installation complete! Press any key to exit...
pause >nul
