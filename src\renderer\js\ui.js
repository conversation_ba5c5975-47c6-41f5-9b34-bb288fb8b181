// UI Management Module for FireTool

class UIManager {
  constructor() {
    this.modals = new Map();
    this.notifications = [];
    this.themes = {
      light: 'light-theme',
      dark: 'dark-theme'
    };
    this.currentTheme = 'light';
    
    this.init();
  }

  init() {
    this.initializeKeyboardShortcuts();
    this.initializeTooltips();
    this.initializeContextMenus();
    this.loadUserPreferences();
  }

  initializeKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + S - Save
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        this.handleSave();
      }
      
      // Ctrl/Cmd + N - New
      if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        this.handleNew();
      }
      
      // Ctrl/Cmd + O - Open
      if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
        e.preventDefault();
        this.handleOpen();
      }
      
      // Ctrl/Cmd + F - Find
      if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        this.focusSearch();
      }
      
      // Escape - Close modal/cancel
      if (e.key === 'Escape') {
        this.handleEscape();
      }
      
      // F5 - Refresh data
      if (e.key === 'F5') {
        e.preventDefault();
        this.refreshCurrentView();
      }
    });
  }

  initializeTooltips() {
    // Simple tooltip implementation
    document.addEventListener('mouseover', (e) => {
      const element = e.target.closest('[data-tooltip]');
      if (element) {
        this.showTooltip(element, element.dataset.tooltip);
      }
    });

    document.addEventListener('mouseout', (e) => {
      const element = e.target.closest('[data-tooltip]');
      if (element) {
        this.hideTooltip();
      }
    });
  }

  initializeContextMenus() {
    document.addEventListener('contextmenu', (e) => {
      // Custom context menu for specific elements
      const element = e.target.closest('[data-context-menu]');
      if (element) {
        e.preventDefault();
        this.showContextMenu(e, element.dataset.contextMenu);
      }
    });

    // Hide context menu on click elsewhere
    document.addEventListener('click', () => {
      this.hideContextMenu();
    });
  }

  // Modal Management
  showModal(id, content, options = {}) {
    const modal = this.createModal(id, content, options);
    document.body.appendChild(modal);
    
    // Animate in
    requestAnimationFrame(() => {
      modal.classList.add('show');
    });

    this.modals.set(id, modal);
    return modal;
  }

  hideModal(id = null) {
    if (id) {
      const modal = this.modals.get(id);
      if (modal) {
        this.removeModal(modal, id);
      }
    } else {
      // Hide all modals
      this.modals.forEach((modal, modalId) => {
        this.removeModal(modal, modalId);
      });
    }
  }

  createModal(id, content, options) {
    const modal = Utils.createElement('div', {
      className: 'modal-overlay',
      id: `modal-${id}`
    });

    const container = Utils.createElement('div', {
      className: `modal-container ${options.size || 'medium'}`,
      innerHTML: content
    });

    modal.appendChild(container);

    // Close on overlay click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.hideModal(id);
      }
    });

    return modal;
  }

  removeModal(modal, id) {
    modal.classList.remove('show');
    setTimeout(() => {
      if (modal.parentNode) {
        modal.parentNode.removeChild(modal);
      }
      this.modals.delete(id);
    }, 300);
  }

  // Notification Management
  showNotification(title, message, type = 'info', duration = 5000) {
    const notification = this.createNotification(title, message, type);
    const container = document.getElementById('notifications') || this.createNotificationContainer();
    
    container.appendChild(notification);
    this.notifications.push(notification);

    // Auto remove
    if (duration > 0) {
      setTimeout(() => {
        this.removeNotification(notification);
      }, duration);
    }

    return notification;
  }

  createNotification(title, message, type) {
    const notification = Utils.createElement('div', {
      className: `notification ${type}`
    });

    notification.innerHTML = `
      <div class="notification-header">
        <span class="notification-title">${Utils.escapeHtml(title)}</span>
        <button class="notification-close">&times;</button>
      </div>
      <div class="notification-message">${Utils.escapeHtml(message)}</div>
    `;

    // Close button
    notification.querySelector('.notification-close').addEventListener('click', () => {
      this.removeNotification(notification);
    });

    return notification;
  }

  createNotificationContainer() {
    const container = Utils.createElement('div', {
      id: 'notifications',
      className: 'notifications-container'
    });
    document.body.appendChild(container);
    return container;
  }

  removeNotification(notification) {
    notification.style.transform = 'translateX(100%)';
    notification.style.opacity = '0';
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
      const index = this.notifications.indexOf(notification);
      if (index > -1) {
        this.notifications.splice(index, 1);
      }
    }, 300);
  }

  // Tooltip Management
  showTooltip(element, text) {
    this.hideTooltip(); // Hide any existing tooltip

    const tooltip = Utils.createElement('div', {
      className: 'tooltip',
      innerHTML: Utils.escapeHtml(text)
    });

    document.body.appendChild(tooltip);

    const rect = element.getBoundingClientRect();
    tooltip.style.position = 'absolute';
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
    tooltip.style.zIndex = '10000';

    this.currentTooltip = tooltip;
  }

  hideTooltip() {
    if (this.currentTooltip) {
      this.currentTooltip.remove();
      this.currentTooltip = null;
    }
  }

  // Context Menu Management
  showContextMenu(event, menuType) {
    this.hideContextMenu();

    const menu = this.createContextMenu(menuType);
    document.body.appendChild(menu);

    // Position menu
    menu.style.position = 'absolute';
    menu.style.left = event.pageX + 'px';
    menu.style.top = event.pageY + 'px';
    menu.style.zIndex = '10000';

    // Adjust position if menu goes off screen
    const rect = menu.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
      menu.style.left = (event.pageX - rect.width) + 'px';
    }
    if (rect.bottom > window.innerHeight) {
      menu.style.top = (event.pageY - rect.height) + 'px';
    }

    this.currentContextMenu = menu;
  }

  createContextMenu(menuType) {
    const menu = Utils.createElement('div', {
      className: 'context-menu'
    });

    const menuItems = this.getContextMenuItems(menuType);
    
    menuItems.forEach(item => {
      if (item.separator) {
        menu.appendChild(Utils.createElement('div', { className: 'menu-separator' }));
      } else {
        const menuItem = Utils.createElement('div', {
          className: 'menu-item',
          innerHTML: `<span class="menu-icon">${item.icon || ''}</span><span class="menu-text">${item.text}</span>`
        });

        menuItem.addEventListener('click', () => {
          item.action();
          this.hideContextMenu();
        });

        menu.appendChild(menuItem);
      }
    });

    return menu;
  }

  getContextMenuItems(menuType) {
    const menus = {
      product: [
        { text: 'Edit', icon: '✏', action: () => console.log('Edit product') },
        { text: 'Duplicate', icon: '📋', action: () => console.log('Duplicate product') },
        { separator: true },
        { text: 'Delete', icon: '🗑', action: () => console.log('Delete product') }
      ],
      grid: [
        { text: 'Copy', icon: '📋', action: () => console.log('Copy') },
        { text: 'Paste', icon: '📄', action: () => console.log('Paste') },
        { separator: true },
        { text: 'Export', icon: '📤', action: () => console.log('Export') }
      ]
    };

    return menus[menuType] || [];
  }

  hideContextMenu() {
    if (this.currentContextMenu) {
      this.currentContextMenu.remove();
      this.currentContextMenu = null;
    }
  }

  // Loading Management
  showLoading(message = 'Loading...', target = null) {
    const loader = Utils.createElement('div', {
      className: 'loading-overlay',
      innerHTML: `
        <div class="loading-content">
          <div class="spinner"></div>
          <div class="loading-message">${Utils.escapeHtml(message)}</div>
        </div>
      `
    });

    if (target) {
      target.style.position = 'relative';
      target.appendChild(loader);
    } else {
      document.body.appendChild(loader);
    }

    return loader;
  }

  hideLoading(loader = null) {
    if (loader) {
      loader.remove();
    } else {
      document.querySelectorAll('.loading-overlay').forEach(el => el.remove());
    }
  }

  // Theme Management
  setTheme(theme) {
    document.body.classList.remove(this.themes[this.currentTheme]);
    document.body.classList.add(this.themes[theme]);
    this.currentTheme = theme;
    this.saveUserPreferences();
  }

  toggleTheme() {
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  // User Preferences
  loadUserPreferences() {
    const preferences = Utils.loadFromStorage('firetool-preferences', {
      theme: 'light',
      gridPageSize: 50,
      autoSave: true
    });

    this.setTheme(preferences.theme);
    this.preferences = preferences;
  }

  saveUserPreferences() {
    this.preferences.theme = this.currentTheme;
    Utils.saveToStorage('firetool-preferences', this.preferences);
  }

  // Keyboard Shortcut Handlers
  handleSave() {
    if (window.fireToolApp) {
      const currentView = window.fireToolApp.currentView;
      
      switch (currentView) {
        case 'boq':
          if (window.boqManager) {
            window.boqManager.saveBOQ();
          }
          break;
        case 'products':
          // Save any pending edits
          break;
        default:
          console.log('Save action for', currentView);
      }
    }
  }

  handleNew() {
    if (window.fireToolApp) {
      const currentView = window.fireToolApp.currentView;
      
      switch (currentView) {
        case 'boq':
          if (window.boqManager) {
            window.boqManager.newBOQ();
          }
          break;
        case 'products':
          window.fireToolApp.addProduct();
          break;
        default:
          console.log('New action for', currentView);
      }
    }
  }

  handleOpen() {
    // Trigger file open dialog
    console.log('Open file');
  }

  focusSearch() {
    const searchInputs = [
      '#product-search',
      '#catalog-search',
      '.search-box input'
    ];

    for (const selector of searchInputs) {
      const input = document.querySelector(selector);
      if (input && input.offsetParent !== null) { // Check if visible
        input.focus();
        input.select();
        break;
      }
    }
  }

  handleEscape() {
    // Close modals first
    if (this.modals.size > 0) {
      this.hideModal();
      return;
    }

    // Hide context menu
    if (this.currentContextMenu) {
      this.hideContextMenu();
      return;
    }

    // Clear search
    const activeSearch = document.activeElement;
    if (activeSearch && activeSearch.type === 'text') {
      activeSearch.value = '';
      activeSearch.blur();
    }
  }

  refreshCurrentView() {
    if (window.fireToolApp) {
      window.fireToolApp.loadViewData(window.fireToolApp.currentView);
      this.showNotification('Refreshed', 'Data refreshed successfully', 'success', 2000);
    }
  }

  // Utility Methods
  confirmAction(message, callback) {
    const confirmed = confirm(message);
    if (confirmed && callback) {
      callback();
    }
    return confirmed;
  }

  promptInput(message, defaultValue = '', callback = null) {
    const result = prompt(message, defaultValue);
    if (result !== null && callback) {
      callback(result);
    }
    return result;
  }
}

// Initialize UI manager
document.addEventListener('DOMContentLoaded', () => {
  window.uiManager = new UIManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = UIManager;
}
