# 🔥 FireTool - Easy Setup Guide (No Coding Experience Required)

## Step 1: Install Node.js (Required)

1. **Go to**: https://nodejs.org/
2. **Download**: Click the big green button that says "LTS" (Long Term Support)
3. **Install**: Run the downloaded file and follow the installation wizard
   - ✅ Accept all default settings
   - ✅ Check "Add to PATH" if asked
   - ✅ Install additional tools if prompted
4. **Restart** your computer after installation

## Step 2: Open Command Prompt

1. **Press**: Windows key + R
2. **Type**: `cmd`
3. **Press**: Enter
4. A black window will open - this is the Command Prompt

## Step 3: Navigate to FireTool Folder

1. **Type** in the Command Prompt:
   ```
   cd "d:\Firetool Beta"
   ```
2. **Press**: Enter

## Step 4: Install FireTool Dependencies

1. **Type** in the Command Prompt:
   ```
   npm install
   ```
2. **Press**: Enter
3. **Wait**: This will take 2-5 minutes (downloading required files)
4. You'll see lots of text scrolling - this is normal!

## Step 5: Start FireTool

1. **Type** in the Command Prompt:
   ```
   npm run dev
   ```
2. **Press**: Enter
3. **Wait**: 10-30 seconds
4. **FireTool window will open automatically!**

## 🎉 Success! FireTool is Running

You should now see the FireTool application window with:
- A header showing "FireTool"
- A sidebar with navigation options
- A main dashboard area

## What You Can Do Now:

### 1. Import Your Excel Data
- Click **"Import Excel"** button
- Select your Excel file with fire safety products
- FireTool will automatically organize your data

### 2. Create a BOQ (Bill of Quantities)
- Click **"BOQ Builder"** in the sidebar
- Drag products from the catalog to your BOQ
- Edit quantities and prices
- Save your BOQ

### 3. Generate Estimates
- Create professional cost estimates
- Export to Excel or PDF
- Share with clients

## Troubleshooting

### Problem: "npm is not recognized"
**Solution**: Node.js wasn't installed properly
- Go back to Step 1 and reinstall Node.js
- Make sure to restart your computer

### Problem: "Cannot find module"
**Solution**: Dependencies didn't install
- Make sure you're in the right folder (Step 3)
- Try running `npm install` again

### Problem: Application won't start
**Solution**: 
1. Close the Command Prompt
2. Open a new Command Prompt
3. Repeat Steps 3-5

### Problem: Black screen or errors
**Solution**:
1. Press Ctrl+C in Command Prompt to stop
2. Type `npm run dev` again
3. If still problems, restart computer and try again

## Getting Help

If you're stuck:
1. **Take a screenshot** of any error messages
2. **Note which step** you're having trouble with
3. **Ask for help** - include the screenshot and step number

## Daily Use

Once everything is working:

### To Start FireTool:
1. Open Command Prompt
2. Type: `cd "d:\Firetool Beta"`
3. Type: `npm run dev`
4. Wait for the window to open

### To Stop FireTool:
- Close the FireTool window, OR
- Press Ctrl+C in the Command Prompt

## Sample Data

I've included sample fire safety product data to get you started:
- Fire Alarm Systems
- Firefighting Equipment
- Clean Agent Systems
- And more...

Check the `sample-data` folder for Excel templates you can use.

## Important Notes

- **Keep the Command Prompt open** while using FireTool
- **Your data is saved locally** on your computer
- **No internet required** for basic use
- **Backup your data** regularly by exporting to Excel

## Next Steps

Once you're comfortable with the basics:
1. **Import your real product data**
2. **Create project templates**
3. **Set up cloud sync** (optional, for team use)
4. **Customize categories** for your specific needs

---

**Remember**: You don't need to understand the code - just follow these steps and FireTool will work perfectly for your fire safety cost estimation needs!
