# Sample Data for FireTool

This directory contains sample data files to help you get started with FireTool.

## Sample Excel Files

### Products Catalog Template
Create an Excel file with the following structure for importing products:

#### Sheet: "Fire Alarm Systems"
| Code | Name | Description | Unit | Base Price | Currency | Supplier |
|------|------|-------------|------|------------|----------|----------|
| FA001 | Smoke Detector | Optical smoke detector | each | 45.00 | USD | ABC Fire Safety |
| FA002 | Heat Detector | Fixed temperature heat detector | each | 35.00 | USD | ABC Fire Safety |
| FA003 | Manual Call Point | Break glass manual call point | each | 25.00 | USD | XYZ Systems |
| FA004 | Fire Alarm Panel | 8 Zone fire alarm control panel | each | 850.00 | USD | XYZ Systems |
| FA005 | Sounder | Electronic sounder 100dB | each | 65.00 | USD | ABC Fire Safety |

#### Sheet: "Firefighting Systems"
| Code | Name | Description | Unit | Base Price | Currency | Supplier |
|------|------|-------------|------|------------|----------|----------|
| FF001 | Sprinkler Head | Standard spray sprinkler 68°C | each | 12.50 | USD | Fire Pro Ltd |
| FF002 | Wet Riser | 100mm wet riser outlet | each | 185.00 | USD | Fire Pro Ltd |
| FF003 | Fire Hose | 25m fire hose with couplings | each | 125.00 | USD | Safety First |
| FF004 | Fire Extinguisher | 6kg ABC dry powder extinguisher | each | 45.00 | USD | Safety First |
| FF005 | Hose Reel | 30m hose reel complete | each | 285.00 | USD | Fire Pro Ltd |

#### Sheet: "Clean Agent Systems"
| Code | Name | Description | Unit | Base Price | Currency | Supplier |
|------|------|-------------|------|------------|----------|----------|
| CA001 | FM200 Cylinder | 50L FM200 cylinder | each | 1250.00 | USD | Clean Fire Systems |
| CA002 | Discharge Nozzle | FM200 discharge nozzle | each | 85.00 | USD | Clean Fire Systems |
| CA003 | Pressure Switch | Low pressure switch | each | 125.00 | USD | Clean Fire Systems |
| CA004 | Control Panel | Clean agent control panel | each | 1850.00 | USD | Clean Fire Systems |

#### Sheet: "Suppliers"
| Name | Contact Person | Email | Phone | Address | Country | Payment Terms | Discount % |
|------|----------------|-------|-------|---------|---------|---------------|------------|
| ABC Fire Safety | John Smith | <EMAIL> | ******-0101 | 123 Fire St, City | USA | Net 30 | 5.0 |
| XYZ Systems | Jane Doe | <EMAIL> | ******-0102 | 456 Safety Ave, City | USA | Net 45 | 7.5 |
| Fire Pro Ltd | Mike Johnson | <EMAIL> | ******-0103 | 789 Protection Blvd, City | USA | Net 30 | 10.0 |
| Safety First | Sarah Wilson | <EMAIL> | ******-0104 | 321 Security Rd, City | USA | Net 60 | 5.0 |
| Clean Fire Systems | Tom Brown | <EMAIL> | ******-0105 | 654 Clean St, City | USA | Net 30 | 12.0 |

#### Sheet: "Labor Rates"
| Category | Skill Level | Hourly Rate | Currency | Region | Effective Date |
|----------|-------------|-------------|----------|--------|----------------|
| Installation | Apprentice | 35.00 | USD | North America | 2024-01-01 |
| Installation | Journeyman | 55.00 | USD | North America | 2024-01-01 |
| Installation | Master | 75.00 | USD | North America | 2024-01-01 |
| Commissioning | Technician | 65.00 | USD | North America | 2024-01-01 |
| Commissioning | Engineer | 95.00 | USD | North America | 2024-01-01 |
| Maintenance | Technician | 45.00 | USD | North America | 2024-01-01 |
| Design | Engineer | 125.00 | USD | North America | 2024-01-01 |
| Project Management | Manager | 150.00 | USD | North America | 2024-01-01 |

## Sample BOQ Template

### Project: Office Building Fire Safety System

| Item Description | Product Code | Unit | Quantity | Unit Price | Total Price | Notes |
|------------------|--------------|------|----------|------------|-------------|-------|
| Smoke detectors for offices | FA001 | each | 25 | 45.00 | 1,125.00 | Ceiling mounted |
| Heat detectors for kitchen | FA002 | each | 3 | 35.00 | 105.00 | High temperature areas |
| Manual call points | FA003 | each | 8 | 25.00 | 200.00 | Near exits |
| Fire alarm control panel | FA004 | each | 1 | 850.00 | 850.00 | Main reception |
| Electronic sounders | FA005 | each | 6 | 65.00 | 390.00 | Each floor |
| Sprinkler heads | FF001 | each | 45 | 12.50 | 562.50 | Office areas |
| Fire extinguishers | FF004 | each | 12 | 45.00 | 540.00 | Various locations |
| Installation labor | - | hours | 120 | 55.00 | 6,600.00 | Journeyman rate |
| Commissioning | - | hours | 16 | 95.00 | 1,520.00 | Engineer rate |
| **SUBTOTAL** | | | | | **11,892.50** | |
| **Markup (15%)** | | | | | **1,783.88** | |
| **TOTAL** | | | | | **13,676.38** | |

## Import Instructions

1. **Prepare your Excel file** with the structure shown above
2. **Save as .xlsx format** for best compatibility
3. **Use consistent column names** for automatic mapping
4. **Include all required fields** (Name, Unit Price, etc.)
5. **Use separate sheets** for different data types

### Column Mapping

FireTool automatically maps common column names:

**Products:**
- `Code` → Product Code
- `Name` → Product Name
- `Description` → Description
- `Unit` → Unit of Measure
- `Base Price` / `Unit Price` / `Price` → Base Price
- `Currency` → Currency
- `Supplier` → Supplier Name

**Suppliers:**
- `Name` → Supplier Name
- `Contact Person` → Contact Person
- `Email` → Email Address
- `Phone` → Phone Number
- `Address` → Address
- `Country` → Country

**Labor Rates:**
- `Category` → Work Category
- `Skill Level` → Skill Level
- `Hourly Rate` / `Rate` → Hourly Rate
- `Currency` → Currency
- `Region` → Region

## Tips for Best Results

1. **Clean your data** before importing
2. **Use consistent naming** for suppliers and categories
3. **Include currency information** for international projects
4. **Validate prices** and quantities
5. **Test with small files** first
6. **Backup existing data** before large imports

## Creating Your Own Templates

1. **Export existing data** to see the format
2. **Modify the structure** as needed
3. **Add your specific categories** and products
4. **Include regional pricing** if applicable
5. **Save as templates** for future use

## Troubleshooting Import Issues

### Common Problems:
- **Missing required columns**: Ensure Name and Price columns exist
- **Invalid data types**: Check that prices are numbers
- **Encoding issues**: Save as UTF-8 if special characters are used
- **Large files**: Break into smaller chunks for better performance

### Solutions:
- **Check the import log** for specific error messages
- **Validate data format** before importing
- **Use CSV format** for problematic Excel files
- **Contact support** if issues persist
