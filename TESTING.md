# FireTool Testing Guide

This guide provides comprehensive testing procedures for the FireTool desktop application.

## Pre-Testing Setup

### 1. Environment Preparation
```bash
# Install dependencies
npm install

# Create test assets (if missing)
mkdir -p assets
# Add placeholder icon files or download from design resources
```

### 2. Database Initialization Test
```bash
# Start in development mode
npm run dev
```

**Expected Results:**
- Application window opens
- Database is created in user data directory
- Default categories are populated
- No console errors

## Core Functionality Tests

### 1. Database Operations

#### Test: Create Product
1. Navigate to **Products** view
2. Click **Add Product** button
3. Fill in product details:
   - Code: TEST001
   - Name: Test Product
   - Description: Test description
   - Unit: each
   - Base Price: 100.00
   - Currency: USD
4. Save product

**Expected Results:**
- Product appears in grid
- Database record created
- No errors in console

#### Test: Edit Product
1. Right-click on test product
2. Select **Edit** from context menu
3. Modify name to "Updated Test Product"
4. Save changes

**Expected Results:**
- Product name updates in grid
- Database record updated
- Grid refreshes automatically

#### Test: Delete Product
1. Select test product
2. Right-click and select **Delete**
3. Confirm deletion

**Expected Results:**
- Product removed from grid
- Soft delete in database (is_active = 0)
- Confirmation dialog appears

### 2. Excel Import/Export

#### Test: Excel Import
1. Create test Excel file with sample data (see sample-data/README.md)
2. Use **File > Import Excel** or drag-and-drop
3. Select test file
4. Review import results

**Expected Results:**
- Import dialog shows progress
- Products imported successfully
- Import history logged
- Data appears in products grid

#### Test: Excel Export
1. Navigate to **Products** view
2. Click **Export Excel** button
3. Choose save location
4. Open exported file

**Expected Results:**
- Excel file created
- All visible data exported
- Proper column headers
- Data integrity maintained

### 3. BOQ Builder

#### Test: Create BOQ
1. Navigate to **BOQ Builder**
2. Enter BOQ title: "Test BOQ"
3. Drag products from catalog to BOQ grid
4. Edit quantities and prices
5. Save BOQ

**Expected Results:**
- Products added to BOQ grid
- Total calculations update automatically
- BOQ saved to database
- Item count and total amount displayed

#### Test: BOQ Calculations
1. Add product with quantity 5, unit price $10
2. Verify total price shows $50
3. Edit quantity to 10
4. Verify total price updates to $100
5. Check BOQ total updates

**Expected Results:**
- Line totals calculate correctly
- BOQ total updates automatically
- No calculation errors
- Real-time updates

### 4. Project Management

#### Test: Create Project
1. Use **File > New Project**
2. Enter project details:
   - Name: Test Project
   - Client: Test Client
   - Location: Test Location
3. Save project

**Expected Results:**
- Project created in database
- Current project indicator updates
- Project appears in projects list

#### Test: Save/Load Project
1. Create project with BOQ data
2. Use **File > Save Project As**
3. Choose location and save
4. Use **File > Open Project**
5. Load saved project

**Expected Results:**
- Project file (.firetool) created
- All project data preserved
- BOQs and estimates restored
- No data loss

### 5. User Interface

#### Test: Navigation
1. Click each sidebar navigation item
2. Verify views switch correctly
3. Test keyboard shortcuts (Ctrl+N, Ctrl+S, etc.)
4. Test search functionality

**Expected Results:**
- All views load without errors
- Active navigation highlighted
- Keyboard shortcuts work
- Search filters data correctly

#### Test: Grid Operations
1. Test sorting by clicking column headers
2. Test filtering using column filters
3. Test row selection (single and multiple)
4. Test context menus
5. Test pagination

**Expected Results:**
- Sorting works in both directions
- Filters apply correctly
- Selection states maintained
- Context menus appear
- Pagination navigates properly

#### Test: Modals and Dialogs
1. Open various modal dialogs
2. Test form validation
3. Test close buttons and ESC key
4. Test overlay click to close

**Expected Results:**
- Modals open and close properly
- Form validation prevents invalid data
- Multiple close methods work
- No modal stacking issues

## Performance Tests

### 1. Large Dataset Handling

#### Test: Import Large Excel File
1. Create Excel file with 1000+ products
2. Import using Excel import feature
3. Monitor performance and memory usage

**Expected Results:**
- Import completes without errors
- Reasonable import time (< 30 seconds)
- Memory usage remains stable
- Application remains responsive

#### Test: Grid Performance
1. Load 1000+ products in grid
2. Test scrolling performance
3. Test sorting and filtering
4. Monitor frame rate

**Expected Results:**
- Smooth scrolling with virtual scrolling
- Fast sorting and filtering
- No UI freezing
- Consistent frame rate

### 2. Memory Usage

#### Test: Memory Leaks
1. Perform repetitive operations:
   - Open/close modals
   - Switch between views
   - Import/export data
2. Monitor memory usage over time

**Expected Results:**
- Memory usage stabilizes
- No continuous memory growth
- Garbage collection working
- No memory leaks detected

## Error Handling Tests

### 1. Database Errors

#### Test: Corrupted Database
1. Manually corrupt database file
2. Start application
3. Verify error handling

**Expected Results:**
- Graceful error message
- Option to restore from backup
- Application doesn't crash
- User guided to solution

#### Test: Disk Space
1. Fill disk to near capacity
2. Attempt database operations
3. Verify error handling

**Expected Results:**
- Clear error messages
- Operations fail gracefully
- No data corruption
- User notified of issue

### 2. File Operation Errors

#### Test: Invalid Excel File
1. Try to import corrupted Excel file
2. Try to import non-Excel file
3. Verify error handling

**Expected Results:**
- Clear error messages
- Import process stops safely
- No partial data imported
- User can retry with valid file

#### Test: File Permissions
1. Try to save to read-only location
2. Try to open file without permissions
3. Verify error handling

**Expected Results:**
- Permission errors caught
- Alternative locations suggested
- No application crash
- Clear user guidance

## Integration Tests

### 1. End-to-End Workflow

#### Test: Complete Project Workflow
1. Create new project
2. Import product catalog
3. Create BOQ with multiple items
4. Generate estimate
5. Export to Excel
6. Save project file

**Expected Results:**
- All steps complete successfully
- Data consistency maintained
- No errors throughout process
- Professional output generated

### 2. Data Integrity

#### Test: Cross-Reference Integrity
1. Create products with categories
2. Create BOQ with products
3. Delete category
4. Verify referential integrity

**Expected Results:**
- Foreign key constraints enforced
- Orphaned records handled
- User warned of dependencies
- Data consistency maintained

## Regression Tests

### 1. Feature Regression
Run all core functionality tests after any code changes:
- Database operations
- Excel import/export
- BOQ builder
- Project management
- UI interactions

### 2. Performance Regression
Monitor performance metrics:
- Application startup time
- Database query performance
- Grid rendering speed
- Memory usage patterns

## Automated Testing

### 1. Unit Tests
```bash
npm test
```

### 2. Integration Tests
```bash
npm run test:integration
```

### 3. End-to-End Tests
```bash
npm run test:e2e
```

## Bug Reporting

### Information to Include
1. **Steps to reproduce**
2. **Expected vs actual behavior**
3. **System information** (OS, Node.js version)
4. **Console errors** (if any)
5. **Screenshots** (if applicable)
6. **Sample data** (if relevant)

### Severity Levels
- **Critical**: Application crashes, data loss
- **High**: Core functionality broken
- **Medium**: Feature partially working
- **Low**: Minor UI issues, cosmetic problems

## Test Data Cleanup

After testing:
1. **Clear test data** from database
2. **Remove test files** from file system
3. **Reset application state**
4. **Clear browser cache** (if applicable)

## Continuous Testing

### Daily Checks
- Application starts without errors
- Core features functional
- No console errors
- Performance acceptable

### Weekly Checks
- Full regression test suite
- Performance benchmarks
- Memory usage analysis
- User feedback review

### Release Testing
- Complete test suite execution
- Cross-platform testing
- Performance validation
- Security review
- Documentation updates

Remember to test on multiple platforms (Windows, macOS, Linux) and with different data sets to ensure comprehensive coverage.
