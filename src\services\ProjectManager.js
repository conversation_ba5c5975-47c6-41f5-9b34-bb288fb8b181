const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class ProjectManager {
  constructor(databaseManager) {
    this.db = databaseManager;
    this.currentProject = null;
    this.projectFileExtension = '.firetool';
  }

  async createProject(projectData) {
    try {
      const project = {
        id: uuidv4(),
        name: projectData.name,
        description: projectData.description || '',
        client_name: projectData.client_name || '',
        project_location: projectData.project_location || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: 1,
        metadata: JSON.stringify(projectData.metadata || {})
      };

      const result = this.db.insert('projects', project);
      this.currentProject = project;
      
      return { success: true, project };
    } catch (error) {
      console.error('Failed to create project:', error);
      throw error;
    }
  }

  async loadProject(projectId) {
    try {
      const project = this.db.findById('projects', projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      this.currentProject = project;
      return { success: true, project };
    } catch (error) {
      console.error('Failed to load project:', error);
      throw error;
    }
  }

  async saveProject() {
    if (!this.currentProject) {
      throw new Error('No active project to save');
    }

    try {
      this.currentProject.updated_at = new Date().toISOString();
      this.db.update('projects', this.currentProject.id, this.currentProject);
      
      return { success: true };
    } catch (error) {
      console.error('Failed to save project:', error);
      throw error;
    }
  }

  async saveProjectToFile(filePath) {
    if (!this.currentProject) {
      throw new Error('No active project to save');
    }

    try {
      // Gather all project data
      const projectData = await this.exportProjectData();
      
      // Save to file
      fs.writeFileSync(filePath, JSON.stringify(projectData, null, 2));
      
      return { success: true, filePath };
    } catch (error) {
      console.error('Failed to save project to file:', error);
      throw error;
    }
  }

  async loadProjectFromFile(filePath) {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error('Project file not found');
      }

      const fileContent = fs.readFileSync(filePath, 'utf8');
      const projectData = JSON.parse(fileContent);
      
      // Import project data
      await this.importProjectData(projectData);
      
      return { success: true, project: this.currentProject };
    } catch (error) {
      console.error('Failed to load project from file:', error);
      throw error;
    }
  }

  async exportProjectData() {
    if (!this.currentProject) {
      throw new Error('No active project to export');
    }

    try {
      // Get all related data
      const boqs = this.db.findAll('boq', { project_id: this.currentProject.id });
      const estimates = this.db.findAll('estimates', { project_id: this.currentProject.id });
      
      // Get BOQ items for each BOQ
      const boqItems = {};
      for (const boq of boqs) {
        boqItems[boq.id] = this.db.findAll('boq_items', { boq_id: boq.id });
      }

      const projectData = {
        version: '1.0',
        exported_at: new Date().toISOString(),
        project: this.currentProject,
        boqs: boqs,
        boq_items: boqItems,
        estimates: estimates
      };

      return projectData;
    } catch (error) {
      console.error('Failed to export project data:', error);
      throw error;
    }
  }

  async importProjectData(projectData) {
    try {
      // Validate project data
      if (!projectData.project) {
        throw new Error('Invalid project data');
      }

      // Create new project with new ID
      const newProject = {
        ...projectData.project,
        id: uuidv4(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Insert project
      this.db.insert('projects', newProject);
      this.currentProject = newProject;

      // Import BOQs
      const boqIdMapping = {};
      if (projectData.boqs) {
        for (const boq of projectData.boqs) {
          const newBoqId = uuidv4();
          boqIdMapping[boq.id] = newBoqId;
          
          const newBoq = {
            ...boq,
            id: newBoqId,
            project_id: newProject.id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          
          this.db.insert('boq', newBoq);
        }
      }

      // Import BOQ items
      if (projectData.boq_items) {
        for (const [oldBoqId, items] of Object.entries(projectData.boq_items)) {
          const newBoqId = boqIdMapping[oldBoqId];
          if (newBoqId) {
            for (const item of items) {
              const newItem = {
                ...item,
                id: uuidv4(),
                boq_id: newBoqId
              };
              
              this.db.insert('boq_items', newItem);
            }
          }
        }
      }

      // Import estimates
      if (projectData.estimates) {
        for (const estimate of projectData.estimates) {
          const newEstimate = {
            ...estimate,
            id: uuidv4(),
            project_id: newProject.id,
            boq_id: estimate.boq_id ? boqIdMapping[estimate.boq_id] : null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          
          this.db.insert('estimates', newEstimate);
        }
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to import project data:', error);
      throw error;
    }
  }

  async getAllProjects() {
    try {
      return this.db.findAll('projects', { is_active: 1 });
    } catch (error) {
      console.error('Failed to get all projects:', error);
      throw error;
    }
  }

  async deleteProject(projectId) {
    try {
      // Soft delete project
      this.db.update('projects', projectId, {
        is_active: 0,
        updated_at: new Date().toISOString()
      });

      if (this.currentProject && this.currentProject.id === projectId) {
        this.currentProject = null;
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to delete project:', error);
      throw error;
    }
  }

  async duplicateProject(projectId, newName) {
    try {
      const originalProject = this.db.findById('projects', projectId);
      if (!originalProject) {
        throw new Error('Project not found');
      }

      // Create new project
      const newProject = {
        ...originalProject,
        id: uuidv4(),
        name: newName,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      this.db.insert('projects', newProject);

      // Duplicate BOQs and related data
      const boqs = this.db.findAll('boq', { project_id: projectId });
      const boqIdMapping = {};

      for (const boq of boqs) {
        const newBoqId = uuidv4();
        boqIdMapping[boq.id] = newBoqId;
        
        const newBoq = {
          ...boq,
          id: newBoqId,
          project_id: newProject.id,
          name: `${boq.name} (Copy)`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        this.db.insert('boq', newBoq);

        // Duplicate BOQ items
        const boqItems = this.db.findAll('boq_items', { boq_id: boq.id });
        for (const item of boqItems) {
          const newItem = {
            ...item,
            id: uuidv4(),
            boq_id: newBoqId
          };
          
          this.db.insert('boq_items', newItem);
        }
      }

      // Duplicate estimates
      const estimates = this.db.findAll('estimates', { project_id: projectId });
      for (const estimate of estimates) {
        const newEstimate = {
          ...estimate,
          id: uuidv4(),
          project_id: newProject.id,
          boq_id: estimate.boq_id ? boqIdMapping[estimate.boq_id] : null,
          estimate_number: this.generateEstimateNumber(),
          title: `${estimate.title} (Copy)`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        this.db.insert('estimates', newEstimate);
      }

      return { success: true, project: newProject };
    } catch (error) {
      console.error('Failed to duplicate project:', error);
      throw error;
    }
  }

  generateEstimateNumber() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const time = String(date.getTime()).slice(-4);
    
    return `EST-${year}${month}${day}-${time}`;
  }

  getCurrentProject() {
    return this.currentProject;
  }

  setCurrentProject(project) {
    this.currentProject = project;
  }

  clearCurrentProject() {
    this.currentProject = null;
  }

  getProjectSummary(projectId = null) {
    const project = projectId ? this.db.findById('projects', projectId) : this.currentProject;
    if (!project) {
      return null;
    }

    try {
      const boqs = this.db.findAll('boq', { project_id: project.id });
      const estimates = this.db.findAll('estimates', { project_id: project.id });
      
      let totalBOQValue = 0;
      let totalItems = 0;

      for (const boq of boqs) {
        totalBOQValue += boq.total_amount || 0;
        const items = this.db.findAll('boq_items', { boq_id: boq.id });
        totalItems += items.length;
      }

      return {
        project,
        totalBOQs: boqs.length,
        totalEstimates: estimates.length,
        totalBOQValue,
        totalItems,
        lastUpdated: project.updated_at
      };
    } catch (error) {
      console.error('Failed to get project summary:', error);
      return null;
    }
  }
}

module.exports = ProjectManager;
