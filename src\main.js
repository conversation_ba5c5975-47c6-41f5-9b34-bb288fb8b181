const { app, BrowserWindow, Menu, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const { autoUpdater } = require('electron-updater');
const Store = require('electron-store');
const DatabaseManager = require('./database/DatabaseManager');
const ExcelImporter = require('./services/ExcelImporter');
const ProjectManager = require('./services/ProjectManager');

// Initialize electron store for settings
const store = new Store();

// Keep a global reference of the window object
let mainWindow;
let databaseManager;
let isDev = process.argv.includes('--dev');

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    show: false,
    titleBarStyle: 'default'
  });

  // Load the app
  mainWindow.loadFile(path.join(__dirname, 'renderer/index.html'));

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Initialize database
    initializeDatabase();

    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
    if (databaseManager) {
      databaseManager.close();
    }
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Project',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-project');
          }
        },
        {
          label: 'Open Project',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: 'FireTool Projects', extensions: ['firetool'] },
                { name: 'All Files', extensions: ['*'] }
              ]
            });

            if (!result.canceled) {
              mainWindow.webContents.send('menu-open-project', result.filePaths[0]);
            }
          }
        },
        {
          label: 'Save Project',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow.webContents.send('menu-save-project');
          }
        },
        {
          label: 'Save Project As...',
          accelerator: 'CmdOrCtrl+Shift+S',
          click: async () => {
            const result = await dialog.showSaveDialog(mainWindow, {
              filters: [
                { name: 'FireTool Projects', extensions: ['firetool'] }
              ]
            });

            if (!result.canceled) {
              mainWindow.webContents.send('menu-save-project-as', result.filePath);
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Import Excel',
          accelerator: 'CmdOrCtrl+I',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: 'Excel Files', extensions: ['xlsx', 'xls'] },
                { name: 'CSV Files', extensions: ['csv'] }
              ]
            });

            if (!result.canceled) {
              mainWindow.webContents.send('menu-import-excel', result.filePaths[0]);
            }
          }
        },
        {
          label: 'Export to Excel',
          accelerator: 'CmdOrCtrl+E',
          click: () => {
            mainWindow.webContents.send('menu-export-excel');
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { label: 'Undo', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'Redo', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'Cut', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'Copy', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'Paste', accelerator: 'CmdOrCtrl+V', role: 'paste' },
        { label: 'Select All', accelerator: 'CmdOrCtrl+A', role: 'selectall' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { label: 'Reload', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'Force Reload', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'Toggle Developer Tools', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'Actual Size', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { label: 'Zoom In', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: 'Zoom Out', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { type: 'separator' },
        { label: 'Toggle Fullscreen', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Database',
      submenu: [
        {
          label: 'Sync with Supabase',
          click: () => {
            mainWindow.webContents.send('menu-sync-database');
          }
        },
        {
          label: 'Backup Database',
          click: () => {
            mainWindow.webContents.send('menu-backup-database');
          }
        },
        {
          label: 'Restore Database',
          click: () => {
            mainWindow.webContents.send('menu-restore-database');
          }
        }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About FireTool',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About FireTool',
              message: 'FireTool v1.0.0',
              detail: 'Cost Estimation and Offer Generation Platform for Fire Safety Systems'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

async function initializeDatabase() {
  try {
    databaseManager = new DatabaseManager();
    await databaseManager.initialize();

    // Send database ready signal to renderer
    mainWindow.webContents.send('database-ready');
  } catch (error) {
    console.error('Failed to initialize database:', error);
    dialog.showErrorBox('Database Error', 'Failed to initialize database: ' + error.message);
  }
}

// App event handlers
app.whenReady().then(() => {
  createWindow();
  createMenu();

  // Handle app activation (macOS)
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });

  // Auto updater
  if (!isDev) {
    autoUpdater.checkForUpdatesAndNotify();
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC handlers for database operations
ipcMain.handle('db-query', async (event, sql, params) => {
  try {
    const stmt = databaseManager.db.prepare(sql);
    return stmt.all(...params);
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
});

ipcMain.handle('db-insert', async (event, table, data) => {
  try {
    return databaseManager.insert(table, data);
  } catch (error) {
    console.error('Database insert error:', error);
    throw error;
  }
});

ipcMain.handle('db-update', async (event, table, id, data) => {
  try {
    return databaseManager.update(table, id, data);
  } catch (error) {
    console.error('Database update error:', error);
    throw error;
  }
});

ipcMain.handle('db-delete', async (event, table, id) => {
  try {
    return databaseManager.delete(table, id);
  } catch (error) {
    console.error('Database delete error:', error);
    throw error;
  }
});

ipcMain.handle('db-find-by-id', async (event, table, id) => {
  try {
    return databaseManager.findById(table, id);
  } catch (error) {
    console.error('Database findById error:', error);
    throw error;
  }
});

ipcMain.handle('db-find-all', async (event, table, conditions) => {
  try {
    return databaseManager.findAll(table, conditions);
  } catch (error) {
    console.error('Database findAll error:', error);
    throw error;
  }
});

// File operations
ipcMain.handle('import-excel', async (event, filePath) => {
  try {
    const excelImporter = new ExcelImporter(databaseManager);
    return await excelImporter.importFile(filePath);
  } catch (error) {
    console.error('Excel import error:', error);
    throw error;
  }
});

ipcMain.handle('export-excel', async (event, data, filePath) => {
  try {
    // Excel export implementation will be added
    return { success: true, filePath };
  } catch (error) {
    console.error('Excel export error:', error);
    throw error;
  }
});

ipcMain.handle('backup-database', async (event) => {
  try {
    const backupPath = databaseManager.backup();
    return { success: true, backupPath };
  } catch (error) {
    console.error('Database backup error:', error);
    throw error;
  }
});

module.exports = { mainWindow, databaseManager };
